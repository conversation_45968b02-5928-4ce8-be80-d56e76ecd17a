import os, sys, json, logging, uuid, time, socket, shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List
from urllib.parse import unquote, quote
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart

# Resource management imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available; resource monitoring disabled.")

from fastapi import (
    FastAPI,
    Request,
    Depends,
    HTTPException,
    Query,
    BackgroundTasks,
    UploadFile,
    File,
    Form,
    status,
    Header,
    Response,
)
from fastapi.responses import (
    JSONResponse,
    RedirectResponse,
    PlainTextResponse,
    HTMLResponse,
    StreamingResponse,
    FileResponse,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTP<PERSON>asi<PERSON>, HTTPBasicCredentials
from starlette.middleware.sessions import SessionMiddleware
from starlette.routing import NoMatchFound
import mimetypes
import tempfile
import traceback
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from config import settings
import msal

# Optional msal-extensions dependency: guard import
try:
    from msal_extensions import FilePersistence, PersistedTokenCache

    MSAL_EXTENSIONS_AVAILABLE = True
except ImportError:
    MSAL_EXTENSIONS_AVAILABLE = False
    FilePersistence = None  # Define dummy class/None if needed later
    PersistedTokenCache = None  # Define dummy class/None if needed later
    logging.warning("msal-extensions not available; persistent token cache disabled.")
# --- End guard ---
import aiohttp
from PIL import Image

# Optional OCR dependency: guard import
try:
    import pytesseract

    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False
    logging.warning("pytesseract not available, image OCR will be disabled.")
import io
import shutil
import openai

# Try to import APScheduler, but don't fail if it's not installed
try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler

    APSCHEDULER_AVAILABLE = True
except ImportError:
    APSCHEDULER_AVAILABLE = False
    logging.warning("APScheduler not available; webhook renewal scheduling disabled.")

# Try to import PyPDF2, but don't fail if it's not available
try:
    import PyPDF2

    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logging.warning(
        "PyPDF2 not available. PDF processing will use fallback methods only."
    )

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings as LlamaSettings,
    get_response_synthesizer,
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import MetadataMode
from llama_index.core.schema import TextNode

# Optional Cohere reranker: guard import
try:
    from llama_index.postprocessor.cohere_rerank import CohereRerank

    COHERE_RERANK_AVAILABLE = True
except ImportError:
    COHERE_RERANK_AVAILABLE = False
    logging.warning("CohereRerank not available; reranking disabled.")

# Import similarity threshold postprocessor
from llama_index.core.postprocessor import SimilarityPostprocessor

# Load environment variables first
load_dotenv()

# ── basic logging ─────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(levelname)-8s  %(name)s │ %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("ddb‑rag")


# ── helpers to figure out *one* canonical base URL ────────────────────────────
def _detect_base_url() -> str:
    """
    Guarantee that every redirect and every piece of client‑side JS
    talks to *exactly* the same origin, so the browser sends the session
    cookie back on XHR requests (localhost ≠ 127.0.0.1 otherwise).
    """
    # Render / Railway / Fly.io etc.
    if ext := os.getenv("RENDER_EXTERNAL_URL"):
        return ext.rstrip("/")

    # Determine port
    port_env = int(os.getenv("PORT", 8082))
    # In development, always use localhost
    if os.getenv("ENV", "development").lower() == "development":
        return f"http://localhost:{port_env}"

    # For other environments, derive from HOST env var
    host_env = os.getenv("HOST") or "127.0.0.1"
    # If HOST is 0.0.0.0 or 127.0.0.1, default to localhost
    if host_env in ("0.0.0.0", "127.0.0.1"):
        host_env = "localhost"
    else:
        # Validate IP format; fallback to localhost on error
        try:
            socket.inet_aton(host_env)
        except OSError:
            host_env = "localhost"

    return f"http://{host_env}:{port_env}"


BASE_URL = _detect_base_url()  # ← http://127.0.0.1:8082
REDIRECT_PATH = "/auth/callback"
EXPECTED_REDIRECT = f"{BASE_URL}{REDIRECT_PATH}"
logger.info(f"OAuth redirect URI →  {EXPECTED_REDIRECT}")

# Application host and port for direct execution
APP_HOST = os.getenv("HOST", "127.0.0.1")
APP_PORT = int(os.getenv("PORT", 8082))


# Helper function to format responses before sending JSON
def format_response(text: str) -> str:
    return text


# ── MSAL configuration ────────────────────────────────────────────────────────
MSAL_SCOPES = [
    "User.Read",
    "Sites.Read.All",
    "Files.Read.All",
    "Sites.ReadWrite.All",
]
MSAL_APP_SCOPE = ["https://graph.microsoft.com/.default"]

# Build the MSAL app instance used for user authentication
msal_app = None
if settings.MS_CLIENT_ID and settings.MS_CLIENT_SECRET and settings.MS_TENANT_ID:
    # Use msal-extensions for file-persistent token cache with automatic locking
    token_cache = None  # Default to in-memory cache
    if MSAL_EXTENSIONS_AVAILABLE:
        try:
            cache_path = settings.DATA_DIR / "token_caches" / "msal_cache.bin"
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            persistence = FilePersistence(str(cache_path))
            token_cache = PersistedTokenCache(persistence)
            logger.info("Using file-persistent MSAL token cache.")
        except Exception as e:
            logger.error(
                f"Failed to initialize file-persistent token cache: {e}. Falling back to in-memory cache."
            )
            token_cache = None  # Ensure fallback on error
    else:
        logger.warning("msal-extensions not found. Using in-memory MSAL token cache.")

    msal_app = msal.ConfidentialClientApplication(
        settings.MS_CLIENT_ID,
        authority=f"https://login.microsoftonline.com/{settings.MS_TENANT_ID}",
        client_credential=settings.MS_CLIENT_SECRET.get_secret_value(),
        token_cache=token_cache,  # Pass the persistent cache or None
    )
else:
    logger.warning(
        "Microsoft credentials (MS_CLIENT_ID, MS_CLIENT_SECRET, MS_TENANT_ID) not configured. SharePoint delegated auth will not work."
    )

# ── FastAPI app and middle‑wares ──────────────────────────────────────────────
app = FastAPI()

# single session cookie, always bound to the host part of BASE_URL
_cookie_host = BASE_URL.split("://", 1)[1].split(":")[
    0
]  # e.g., 'localhost' or real domain
# Only set domain for real hosts; omit for localhost to create host-only cookie
cookie_domain = None if _cookie_host in ("localhost", "127.0.0.1") else _cookie_host
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SESSION_SECRET_KEY.get_secret_value(),
    session_cookie="rag_session",
    domain=cookie_domain,
    same_site="lax",
    max_age=3600 * 24 * 7,
    https_only=BASE_URL.startswith("https"),
)

# Initialize global variables
llm = None
embed_model = None
index = None  # Will be initialized in startup

# Model configuration is now handled through settings.OPENAI_MODEL

# Configure CORS with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origin_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "text/event-stream"],
)

# Mount static files with defensive check and templates
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Static files mounted from 'static' directory")
else:
    logger.warning("'static' folder not found – skipping mount")
templates = Jinja2Templates(directory="templates")


@app.get("/config.js")
async def config_js():
    # the browser will evaluate this script before running its own JS
    return PlainTextResponse(
        f"window.__RAG_BASE = '{BASE_URL}';",
        media_type="application/javascript",
    )


# --- Auth Helpers ---
def _extract_user(session):
    """Return a user-dict if either auth method is present."""
    if "basic_user" in session and session["basic_user"].get("authenticated"):
        return session["basic_user"]
    if "ms_user" in session:
        return session["ms_user"]
    return None


async def get_current_user(request: Request):
    """Get current user from session using either authentication method."""
    session = request.session
    # <<< Log session content whenever user is checked >>>
    logger.debug(f"[Auth Check] Path: {request.url.path}, Session: {dict(session)}")
    # <<< End log >>>
    user = _extract_user(session)
    if user:
        return user
    raise HTTPException(
        status_code=401,
        detail="Not authenticated",
    )


# Create unified security object and dependency
security = HTTPBasic()
CurrentUser = Depends(get_current_user)


# --- Function to get Application Token ---
async def get_application_token() -> Optional[str]:
    """Acquire an access token for the application itself using client credentials."""
    if not msal_app:
        logger.error("MSAL application not initialized. Cannot get application token.")
        return None

    try:
        # Attempt to get token from cache first (MSAL handles caching)
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_APP_SCOPE, None),
            timeout=10,
        )

        if not result:
            logger.info("No suitable app token in cache, acquiring new one...")
            result = await asyncio.wait_for(
                asyncio.to_thread(msal_app.acquire_token_for_client, MSAL_APP_SCOPE),
                timeout=10,
            )

        if "access_token" in result:
            logger.info("Successfully acquired application access token.")
            return result["access_token"]
        else:
            logger.error(
                f"Failed to acquire application token: {result.get('error_description', 'No error description')}"
            )
            return None
    except Exception as e:
        logger.error(f"Exception acquiring application token: {e}", exc_info=True)
        return None


@app.on_event("startup")
async def startup_event():
    """Initialize application state and create required directories and schedule heavy tasks."""
    logger.info("Starting application with document persistence...")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Environment: {os.getenv('ENV', 'development')}")

    # <<< Add route logging >>>
    logger.info("Registered routes:")
    for r in app.routes:
        if hasattr(r, "methods"):  # Check if it's a route with methods
            try:
                logger.info(
                    f"  - Route: Path={r.path}, Name={r.name}, Methods={r.methods}"
                )
            except AttributeError:  # Handle routes without a name
                logger.info(f"  - Route: Path={r.path}, Methods={r.methods}")
        elif hasattr(r, "path"):  # Handle Mounts (like static files)
            logger.info(f"  - Mount: Path={r.path}")
        else:
            logger.info(f"  - Other: {r}")  # Log anything else unexpected
    # <<< End route logging >>>

    # Create required directories
    try:
        settings.STORAGE_DIR.mkdir(exist_ok=True)
        settings.DATA_DIR.mkdir(exist_ok=True)
        settings.UPLOAD_DIR.mkdir(exist_ok=True)
        settings.TEMP_DIR.mkdir(exist_ok=True)
        logger.info("Storage directory ready")
    except Exception as e:
        logger.error(f"Error creating directories: {e}")

    # Schedule heavy initialization in background
    loop = asyncio.get_running_loop()
    loop.create_task(initialise_heavy_components())
    logger.info("Scheduled background initialization of heavy components")
    
    # Make msal_app available to background sync
    app.state.msal_app = msal_app


# Background initialization moved out of startup_event
async def initialise_heavy_components():
    """Runs expensive initialization after the app has started."""
    try:
        # Initialize OpenAI components
        logger.info("Initializing OpenAI model...")
        # Validate model name before initialization
        validate_model_name(settings.OPENAI_MODEL)
        logger.info(f"Using validated OpenAI model: {settings.OPENAI_MODEL}")
        
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        app.state.llm = await asyncio.to_thread(
            LlamaOpenAI, model=settings.OPENAI_MODEL, temperature=settings.LLM_TEMPERATURE, max_tokens=settings.LLM_MAX_TOKENS
        )
        logger.info("OpenAI LLM initialized successfully")

        # Initialize embedding model
        logger.info("Initializing embedding model...")
        app.state.embed_model = await asyncio.to_thread(OpenAIEmbedding, model="text-embedding-3-small")
        logger.info("Embedding model initialized successfully")
        
        # Set global LlamaSettings with the initialized models for consistent behavior
        # This ensures all LlamaIndex components use the same model instances
        logger.info("Setting global LlamaSettings with initialized models...")
        LlamaSettings.llm = app.state.llm
        LlamaSettings.embed_model = app.state.embed_model
        logger.info("Global LlamaSettings configured successfully")

        # Initialize Cohere reranker if the module is available
        if COHERE_RERANK_AVAILABLE:
            try:
                logger.info("Initializing Cohere reranker...")
                app.state.reranker = await asyncio.to_thread(
                    CohereRerank, 
                    api_key=settings.COHERE_API_KEY.get_secret_value(),
                    model="rerank-english-v3.0"
                )
                logger.info("Cohere reranker initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing Cohere reranker: {e}")
                app.state.reranker = None
        else:
            app.state.reranker = None
            logger.info("CohereRerank not available; skipping initialization")

        # Initialize SharePoint client if enabled
        if settings.USE_SHAREPOINT:
            try:
                logger.info("Initializing SharePoint client...")
                app.state.sharepoint_client = await asyncio.to_thread(SharePointClient)
                logger.info("SharePoint client initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing SharePoint client: {e}")

        # Load vector index
        try:
            logger.info("Loading vector index...")
            app.state.index = await load_index()
            logger.info("Index loaded successfully")
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            logger.warning("Creating empty index as fallback...")
            app.state.index = await create_empty_index()

        # Create query engine
        try:
            app.state.query_engine = get_query_engine(
                app.state.index, app.state.reranker
            )
            logger.info("Query engine created successfully")
        except Exception as e:
            logger.error(f"Error creating query engine: {e}")

        # Start background sync if enabled and SharePoint is configured
        if settings.AUTO_SYNC_ENABLED and settings.USE_SHAREPOINT and settings.SYNC_TARGET_DRIVE_ID:
            logger.info("Starting background sync task...")
            asyncio.create_task(background_sync_task())
        else:
            logger.info("Background sync disabled or not configured")
        
        # Initialize webhook subscription management if configured
        if settings.WEBHOOK_NOTIFICATION_URL and settings.SYNC_TARGET_DRIVE_ID:
            logger.info("Starting webhook subscription management...")
            asyncio.create_task(webhook_subscription_manager())
        else:
            logger.info("Webhook subscriptions not configured")

    except Exception as e:
        logger.error(f"Error in background initialization: {e}", exc_info=True)


async def webhook_subscription_manager():
    """Background task to manage webhook subscriptions - create, renew, and monitor."""
    try:
        # Wait for application to be fully initialized
        await asyncio.sleep(60)
        
        # Check every 6 hours for webhook subscription health
        check_interval = 6 * 60 * 60  # 6 hours in seconds
        
        while True:
            try:
                logger.info("Checking webhook subscription health...")
                
                # Try to get a service principal token for webhook management
                msal_app = getattr(app.state, 'msal_app', None)
                if not msal_app:
                    logger.warning("No MSAL app available for webhook management")
                    await asyncio.sleep(check_interval)
                    continue
                
                # Get service principal token
                result = msal_app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
                if not result or "access_token" not in result:
                    logger.warning(f"Failed to acquire token for webhook management: {result.get('error_description', 'Unknown error')}")
                    await asyncio.sleep(check_interval)
                    continue
                
                token = result["access_token"]
                
                # Check existing subscriptions
                subscriptions = getattr(app.state, 'webhook_subscriptions', {})
                current_time = datetime.now()
                
                active_subscriptions = 0
                expired_subscriptions = 0
                
                for sub_id, sub_data in list(subscriptions.items()):
                    try:
                        expiration_time = datetime.fromisoformat(sub_data["expiration_datetime"].replace('Z', '+00:00'))
                        time_until_expiry = expiration_time.replace(tzinfo=None) - current_time
                        
                        if time_until_expiry.total_seconds() < 0:
                            # Subscription expired
                            logger.info(f"Removing expired webhook subscription: {sub_id}")
                            subscriptions.pop(sub_id)
                            expired_subscriptions += 1
                        elif time_until_expiry.days < 1:
                            # Subscription expiring soon, renew it
                            logger.info(f"Renewing webhook subscription: {sub_id}")
                            try:
                                renewed_sub = await app.state.sharepoint_client.renew_webhook_subscription(
                                    subscription_id=sub_id,
                                    token=token
                                )
                                if renewed_sub:
                                    sub_data["expiration_datetime"] = renewed_sub["expirationDateTime"]
                                    sub_data["status"] = "renewed"
                                    active_subscriptions += 1
                                    logger.info(f"Successfully renewed webhook subscription: {sub_id}")
                                else:
                                    logger.warning(f"Failed to renew webhook subscription: {sub_id}")
                            except Exception as e:
                                logger.error(f"Error renewing webhook subscription {sub_id}: {e}")
                        else:
                            active_subscriptions += 1
                            
                    except Exception as e:
                        logger.error(f"Error checking subscription {sub_id}: {e}")
                
                # If no active subscriptions, create a new one
                if active_subscriptions == 0 and settings.WEBHOOK_NOTIFICATION_URL and settings.SYNC_TARGET_DRIVE_ID:
                    logger.info("No active webhook subscriptions found, creating new one...")
                    try:
                        subscription = await app.state.sharepoint_client.create_webhook_subscription(
                            drive_id=settings.SYNC_TARGET_DRIVE_ID,
                            notification_url=settings.WEBHOOK_NOTIFICATION_URL,
                            token=token
                        )
                        
                        # Store subscription details
                        if not hasattr(app.state, 'webhook_subscriptions'):
                            app.state.webhook_subscriptions = {}
                        
                        app.state.webhook_subscriptions[subscription["id"]] = {
                            "subscription_id": subscription["id"],
                            "drive_id": settings.SYNC_TARGET_DRIVE_ID,
                            "notification_url": settings.WEBHOOK_NOTIFICATION_URL,
                            "expiration_datetime": subscription["expirationDateTime"],
                            "created_at": datetime.now().isoformat(),
                            "created_by": "system",
                            "status": "active"
                        }
                        
                        # Log successful creation
                        log_admin_notification(
                            "webhook_subscription_auto_created",
                            f"✅ Automatic webhook subscription created for real-time notifications",
                            {
                                "subscription_id": subscription["id"],
                                "drive_id": settings.SYNC_TARGET_DRIVE_ID,
                                "notification_url": settings.WEBHOOK_NOTIFICATION_URL,
                                "expiration_datetime": subscription["expirationDateTime"],
                                "created_by": "system"
                            }
                        )
                        
                        logger.info(f"Successfully created webhook subscription: {subscription['id']}")
                        
                    except Exception as e:
                        logger.error(f"Error creating webhook subscription: {e}")
                        
                        # Log failed creation
                        log_admin_notification(
                            "webhook_subscription_auto_failed",
                            f"❌ Failed to create automatic webhook subscription",
                            {
                                "error": str(e),
                                "drive_id": settings.SYNC_TARGET_DRIVE_ID,
                                "notification_url": settings.WEBHOOK_NOTIFICATION_URL
                            }
                        )
                
                # Log subscription health summary
                logger.info(f"Webhook subscription health: {active_subscriptions} active, {expired_subscriptions} expired")
                
                if active_subscriptions > 0:
                    log_admin_notification(
                        "webhook_health_good",
                        f"✅ Webhook subscriptions healthy: {active_subscriptions} active",
                        {
                            "active_subscriptions": active_subscriptions,
                            "expired_subscriptions": expired_subscriptions,
                            "check_time": datetime.now().isoformat()
                        }
                    )
                
            except Exception as e:
                logger.error(f"Error in webhook subscription management: {e}", exc_info=True)
            
            # Wait before next check
            await asyncio.sleep(check_interval)
            
    except Exception as e:
        logger.error(f"Fatal error in webhook subscription manager: {e}", exc_info=True)


async def webhook_triggered_sync(change_type: str, resource: str):
    """Immediate sync triggered by webhook notification."""
    try:
        logger.info(f"Starting webhook-triggered sync for {change_type} change on {resource}")
        
        # Get token for sync operation
        msal_app = getattr(app.state, 'msal_app', None)
        if not msal_app:
            logger.error("No MSAL app available for webhook-triggered sync")
            return
        
        # Try service principal token first
        result = msal_app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
        if not result or "access_token" not in result:
            logger.error(f"Failed to acquire token for webhook sync: {result.get('error_description', 'Unknown error')}")
            return
        
        token = result["access_token"]
        
        # Perform targeted sync based on change type
        sync_start_time = datetime.now()
        
        try:
            # Get files from configured folder
            sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
            known_working_drive_id = settings.SYNC_TARGET_DRIVE_ID or "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
            
            logger.info(f"Webhook sync checking folder: '{sync_folder}'")
            files_list = await app.state.sharepoint_client.list_files(
                drive_id=known_working_drive_id, 
                token=token, 
                folder_path=sync_folder
            )
            
            # Get current indexed documents
            all_indexed_docs = []
            if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                all_indexed_docs = list(app.state.index.docstore.docs.values())
            
            # Quick change detection
            new_files_found = []
            updated_files_found = []
            
            for item in files_list:
                if item.get("file"):
                    item_id = item.get("id")
                    item_name = item.get("name")
                    
                    existing_docs = [
                        doc for doc in all_indexed_docs 
                        if doc.metadata.get("sharepoint_id") == item_id
                    ]
                    
                    if not existing_docs:
                        new_files_found.append(item)
                    else:
                        # Check if file was modified
                        item_modified = item.get("lastModifiedDateTime")
                        existing_modified = existing_docs[0].metadata.get("last_modified")
                        
                        if item_modified and existing_modified and item_modified != existing_modified:
                            updated_files_found.append(item)
            
            # Process changes based on settings
            sync_actions_taken = []
            
            if change_type in ["created", "updated"] and settings.AUTO_IMPORT_ENABLED:
                # Auto-import new/updated files if enabled
                files_to_process = new_files_found + updated_files_found
                
                for file_item in files_to_process[:5]:  # Limit to 5 files per webhook
                    try:
                        file_name = file_item.get("name", "Unknown")
                        logger.info(f"Webhook sync processing: {file_name}")
                        
                        # Download and process the file
                        file_content = await app.state.sharepoint_client.download_file(
                            drive_id=known_working_drive_id,
                            file_id=file_item["id"],
                            token=token
                        )
                        
                        if file_content:
                            # Process and index the file
                            temp_file_path = settings.TEMP_DIR / f"webhook_{file_item['id']}_{file_name}"
                            with open(temp_file_path, "wb") as f:
                                f.write(file_content)
                            
                            try:
                                # Process the file and add to index
                                processed_docs = process_uploaded_file(
                                    temp_file_path, 
                                    file_name, 
                                    file_item["id"],
                                    file_item
                                )
                                
                                if processed_docs:
                                    for doc in processed_docs:
                                        app.state.index.insert(doc)
                                    
                                    # Persist changes
                                    app.state.index.storage_context.persist(
                                        persist_dir=str(settings.STORAGE_DIR)
                                    )
                                    
                                    sync_actions_taken.append(f"Imported {file_name}")
                                    logger.info(f"Successfully imported {file_name} via webhook")
                                
                            finally:
                                # Clean up temp file
                                if temp_file_path.exists():
                                    temp_file_path.unlink()
                                    
                    except Exception as e:
                        logger.error(f"Error processing file {file_name} in webhook sync: {e}")
                        sync_actions_taken.append(f"Failed to import {file_name}: {str(e)}")
            
            elif change_type == "deleted" and settings.AUTO_DELETE_ENABLED:
                # Handle deletions if enabled
                # This is more complex as we need to identify which file was deleted
                # For now, we'll just log it and let the regular sync handle it
                logger.info("File deletion detected, will be handled by next regular sync")
                sync_actions_taken.append("Deletion detected (handled by regular sync)")
            
            # Update sync stats
            sync_duration = (datetime.now() - sync_start_time).total_seconds()
            
            app.state.last_sync_stats.update({
                "webhook_sync_completed": True,
                "webhook_sync_duration": sync_duration,
                "webhook_actions_taken": sync_actions_taken,
                "webhook_new_files": len(new_files_found),
                "webhook_updated_files": len(updated_files_found),
                "webhook_sync_timestamp": datetime.now().isoformat()
            })
            
            # Send notification about webhook sync results
            if sync_actions_taken:
                log_admin_notification(
                    "webhook_sync_completed",
                    f"⚡ Real-time sync completed: {len(sync_actions_taken)} actions taken",
                    {
                        "change_type": change_type,
                        "resource": resource,
                        "actions_taken": sync_actions_taken,
                        "sync_duration": sync_duration,
                        "new_files": len(new_files_found),
                        "updated_files": len(updated_files_found)
                    }
                )
            else:
                log_admin_notification(
                    "webhook_sync_no_action",
                    f"⚡ Real-time sync completed: No actions needed",
                    {
                        "change_type": change_type,
                        "resource": resource,
                        "reason": "Auto-import disabled or no eligible files found",
                        "sync_duration": sync_duration
                    }
                )
            
            logger.info(f"Webhook-triggered sync completed in {sync_duration:.2f}s, {len(sync_actions_taken)} actions taken")
            
        except Exception as e:
            logger.error(f"Error during webhook-triggered sync: {e}", exc_info=True)
            
            # Log sync failure
            log_admin_notification(
                "webhook_sync_failed",
                f"❌ Real-time sync failed: {str(e)}",
                {
                    "change_type": change_type,
                    "resource": resource,
                    "error": str(e),
                    "sync_duration": (datetime.now() - sync_start_time).total_seconds()
                }
            )
    
    except Exception as e:
        logger.error(f"Fatal error in webhook-triggered sync: {e}", exc_info=True)


def check_webhook_health() -> dict:
    """
    Check the health of webhook subscriptions and recent activity.
    
    Returns:
        dict: Webhook health status and metrics
    """
    try:
        health_status = {
            "has_active_subscriptions": False,
            "active_subscription_count": 0,
            "expired_subscription_count": 0,
            "last_notification_timestamp": None,
            "last_notification_hours_ago": None,
            "webhook_notifications_today": 0,
            "overall_health": "unknown"
        }
        
        # Check subscription status
        subscriptions = getattr(app.state, 'webhook_subscriptions', {})
        current_time = datetime.now()
        
        active_count = 0
        expired_count = 0
        
        for sub_id, sub_data in subscriptions.items():
            try:
                expiration_time = datetime.fromisoformat(sub_data["expiration_datetime"].replace('Z', '+00:00'))
                if expiration_time.replace(tzinfo=None) > current_time:
                    active_count += 1
                else:
                    expired_count += 1
            except Exception as e:
                logger.warning(f"Error checking subscription {sub_id}: {e}")
                expired_count += 1
        
        health_status["active_subscription_count"] = active_count
        health_status["expired_subscription_count"] = expired_count
        health_status["has_active_subscriptions"] = active_count > 0
        
        # Check recent webhook activity
        if hasattr(app.state, 'pending_webhook_notifications'):
            notifications = app.state.pending_webhook_notifications or []
            
            # Count today's notifications
            today = datetime.now().date()
            today_notifications = [
                n for n in notifications 
                if datetime.fromisoformat(n.get('timestamp', '')).date() == today
            ]
            health_status["webhook_notifications_today"] = len(today_notifications)
            
            # Find most recent notification
            if notifications:
                most_recent = max(
                    notifications, 
                    key=lambda n: n.get('timestamp', '1970-01-01T00:00:00')
                )
                health_status["last_notification_timestamp"] = most_recent.get('timestamp')
                
                try:
                    last_time = datetime.fromisoformat(most_recent.get('timestamp', ''))
                    hours_ago = (current_time - last_time).total_seconds() / 3600
                    health_status["last_notification_hours_ago"] = round(hours_ago, 1)
                except Exception:
                    health_status["last_notification_hours_ago"] = 999
        
        # Determine overall health
        if health_status["has_active_subscriptions"]:
            if health_status["webhook_notifications_today"] > 0:
                health_status["overall_health"] = "excellent"
            elif health_status.get("last_notification_hours_ago", 999) < 24:
                health_status["overall_health"] = "good"
            elif health_status.get("last_notification_hours_ago", 999) < 72:
                health_status["overall_health"] = "fair"
            else:
                health_status["overall_health"] = "poor"
        else:
            health_status["overall_health"] = "no_webhooks"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Error checking webhook health: {e}")
        return {
            "has_active_subscriptions": False,
            "overall_health": "error",
            "error": str(e)
        }


async def background_sync_task():
    """Background task that periodically syncs with SharePoint."""
    sync_interval_seconds = settings.SYNC_INTERVAL_MINUTES * 60
    logger.info(f"Background sync task started, syncing every {settings.SYNC_INTERVAL_MINUTES} minutes")
    
    # Enhanced error handling and recovery state
    consecutive_errors = 0
    last_error_time = None
    sync_disabled = False
    base_retry_delay = 60  # Start with 1 minute delay
    max_retry_delay = 3600  # Max 1 hour delay
    
    # Initialize sync stats on startup
    app.state.last_sync_stats = {
        "status": "initializing",
        "consecutive_errors": 0,
        "sync_disabled": False,
        "last_error": None
    }
    
    # Wait a bit after startup to ensure everything is initialized
    await asyncio.sleep(30)
    
    while True:
        sync_start_time = datetime.now()
        current_retry_delay = sync_interval_seconds
        
        try:
            # Check if sync is disabled due to too many errors
            if sync_disabled:
                logger.warning(f"Background sync disabled due to {consecutive_errors} consecutive errors")
                # Try to re-enable after extended delay
                if last_error_time and (datetime.now() - last_error_time).total_seconds() > max_retry_delay:
                    logger.info("Attempting to re-enable background sync after extended delay")
                    sync_disabled = False
                    consecutive_errors = 0
                else:
                    await asyncio.sleep(sync_interval_seconds)
                    continue
            
            logger.info(f"Running background sync... (consecutive errors: {consecutive_errors})")
            
            # Check webhook health and determine if backup polling is needed
            webhook_health = check_webhook_health()
            backup_polling_needed = (
                not webhook_health.get('has_active_subscriptions', False) or
                webhook_health.get('last_notification_hours_ago', 999) > 6 or
                consecutive_errors > 2
            )
            
            if backup_polling_needed:
                logger.info("🔄 Running backup polling sync due to webhook health issues")
                sync_mode = "backup_polling"
            else:
                logger.info("✅ Webhook health good, running standard sync")
                sync_mode = "standard"
            
            # Check system resources before proceeding
            should_skip, resource_reason = should_skip_sync_due_to_resources()
            if should_skip:
                logger.warning(f"Skipping background sync due to resource constraints: {resource_reason}")
                app.state.last_sync_stats = {
                    "status": "skipped",
                    "skip_reason": resource_reason,
                    "consecutive_errors": consecutive_errors,
                    "sync_disabled": sync_disabled,
                    "resource_check": check_system_resources()
                }
                await asyncio.sleep(sync_interval_seconds)
                continue
            
            # Check if we have the necessary components
            if not hasattr(app.state, 'sharepoint_client') or not app.state.sharepoint_client:
                logger.warning("SharePoint client not available, skipping sync")
                await asyncio.sleep(sync_interval_seconds)
                continue
                
            if not hasattr(app.state, 'index') or not app.state.index:
                logger.warning("Index not available, skipping sync")
                await asyncio.sleep(sync_interval_seconds)
                continue
            
            # Try to get a cached admin token with refresh capability
            cached_token = None
            try:
                # Use the new token refresh mechanism
                msal_app = getattr(app.state, 'msal_app', None)
                
                # First try to get cached user token
                cached_token = await app.state.sharepoint_client.get_cached_token_with_refresh(
                    msal_app=msal_app,
                    cache_dir=Path("storage/data/token_caches")
                )
                
                if cached_token:
                    # Validate and refresh if needed
                    cached_token = await app.state.sharepoint_client.validate_and_refresh_token(
                        token=cached_token,
                        msal_app=msal_app
                    )
                
                # If no cached token, try to get service principal token
                if not cached_token and msal_app:
                    logger.info("No cached user token available, trying service principal token")
                    try:
                        # Try to get application token (service principal)
                        scopes = ["https://graph.microsoft.com/.default"]
                        result = msal_app.acquire_token_for_client(scopes=scopes)
                        
                        if result and "access_token" in result:
                            cached_token = result["access_token"]
                            logger.info("Successfully acquired service principal token for background sync")
                            
                            # Save this token for future use
                            await app.state.sharepoint_client.save_token_to_cache(
                                token=cached_token,
                                expires_in=result.get("expires_in", 3600),
                                account_id="service_principal",
                                cache_dir=Path("storage/data/token_caches")
                            )
                        else:
                            logger.warning(f"Failed to acquire service principal token: {result.get('error_description', 'Unknown error')}")
                    except Exception as e:
                        logger.error(f"Error getting service principal token: {e}")
                
                if not cached_token:
                    logger.info("No valid cached admin token available for background sync")
                    # Store stats indicating no token
                    app.state.last_sync_stats = {
                        "error": "No valid token available",
                        "sync_needed": False,
                        "token_available": False
                    }
                
                if cached_token:
                    # Additional token validation and refresh before using
                    try:
                        # Validate token with a quick test call
                        validated_token = await app.state.sharepoint_client.validate_and_refresh_token(
                            token=cached_token,
                            msal_app=msal_app
                        )
                        
                        if validated_token:
                            cached_token = validated_token
                            logger.info("Token validated successfully for background sync")
                        else:
                            logger.warning("Token validation failed, attempting service principal fallback")
                            # Try service principal as fallback
                            if msal_app:
                                result = msal_app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
                                if result and "access_token" in result:
                                    cached_token = result["access_token"]
                                    logger.info("Service principal fallback successful")
                                else:
                                    logger.error("Service principal fallback failed")
                                    cached_token = None
                    except Exception as e:
                        logger.error(f"Error during token validation: {e}")
                        cached_token = None
                    
                    if cached_token:
                        # Enhanced background sync with optional import functionality
                        sync_mode = "detection" if not settings.AUTO_IMPORT_ENABLED else "import"
                        logger.info(f"Running automated background sync in {sync_mode} mode...")
                        known_working_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
                    else:
                        logger.error("No valid token available after validation attempts")
                        # Update stats to indicate token failure
                        app.state.last_sync_stats = {
                            "error": "Token validation failed",
                            "sync_needed": False,
                            "token_available": False,
                            "last_attempt": datetime.now().isoformat()
                        }
                        consecutive_errors += 1
                        continue
                    
                    try:
                        # Get files from configured folder
                        sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
                        logger.info(f"Background sync checking folder: '{sync_folder}' (configured: '{settings.SYNC_TARGET_FOLDER_PATH}')")
                        files_list = await app.state.sharepoint_client.list_files(
                            drive_id=known_working_drive_id, 
                            token=cached_token, 
                            folder_path=sync_folder
                        )
                        
                        logger.info(f"Background sync found {len(files_list)} items in {sync_folder}")
                        
                        # Initialize counters and tracking
                        new_files_count = 0
                        imported_files_count = 0
                        skipped_files_count = 0
                        error_files_count = 0
                        files_processed = 0
                        
                        all_indexed_docs = []
                        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                            all_indexed_docs = list(app.state.index.docstore.docs.values())
                        
                        # Process items with optional import
                        async def process_items(items, folder_path=""):
                            nonlocal new_files_count, imported_files_count, skipped_files_count, error_files_count, files_processed
                            
                            for item in items:
                                # Check file processing limit
                                if files_processed >= settings.MAX_FILES_PER_SYNC:
                                    logger.warning(f"Background sync reached file limit ({settings.MAX_FILES_PER_SYNC}), stopping")
                                    break
                                
                                if item.get("file"):
                                    item_id = item.get("id")
                                    item_name = item.get("name")
                                    item_size = item.get("size", 0)
                                    
                                    # Check if file already exists in index
                                    existing_docs = [
                                        doc for doc in all_indexed_docs 
                                        if doc.metadata.get("sharepoint_id") == item_id
                                    ]
                                    
                                    if not existing_docs:
                                        new_files_count += 1
                                        files_processed += 1
                                        
                                        # Import logic (only if AUTO_IMPORT_ENABLED)
                                        if settings.AUTO_IMPORT_ENABLED:
                                            # Check file size limit for auto import
                                            if item_size > settings.MAX_FILE_SIZE_AUTO_IMPORT:
                                                logger.info(f"Background sync skipping large file: {item_name} ({item_size} bytes)")
                                                skipped_files_count += 1
                                                continue
                                            
                                            try:
                                                logger.info(f"Background sync importing: {item_name}")
                                                
                                                # Download file to temp directory
                                                temp_dir = Path("storage/temp")
                                                temp_dir.mkdir(parents=True, exist_ok=True)
                                                
                                                file_path = temp_dir / item_name
                                                await app.state.sharepoint_client.download_file(
                                                    drive_id=known_working_drive_id,
                                                    file_id=item_id,
                                                    file_path=str(file_path),
                                                    token=cached_token
                                                )
                                                
                                                # Process file and add to index (similar to manual sync logic)
                                                if hasattr(app.state, 'document_manager') and app.state.document_manager:
                                                    docs = await app.state.document_manager.process_file(
                                                        file_path=str(file_path),
                                                        sharepoint_id=item_id,
                                                        additional_metadata={
                                                            "source": "background_sync",
                                                            "import_timestamp": datetime.now().isoformat(),
                                                            "sharepoint_size": item_size
                                                        }
                                                    )
                                                    
                                                    if docs:
                                                        # Add to index
                                                        for doc in docs:
                                                            app.state.index.insert(doc)
                                                        
                                                        # Persist changes
                                                        app.state.index.storage_context.persist(persist_dir="storage")
                                                        imported_files_count += 1
                                                        logger.info(f"Background sync successfully imported: {item_name}")
                                                    else:
                                                        logger.warning(f"Background sync failed to process: {item_name}")
                                                        error_files_count += 1
                                                else:
                                                    logger.warning("Document manager not available for background import")
                                                    error_files_count += 1
                                                
                                                # Clean up temp file
                                                try:
                                                    file_path.unlink()
                                                except Exception as e:
                                                    logger.warning(f"Failed to clean up temp file {file_path}: {e}")
                                                    
                                            except Exception as e:
                                                logger.error(f"Background sync import error for {item_name}: {e}")
                                                error_files_count += 1
                                        else:
                                            logger.info(f"Background sync detected new file: {item_name}")
                                            
                                elif item.get("folder"):
                                    try:
                                        subfolder_path = f"{folder_path}/{item.get('name')}" if folder_path else item.get('name')
                                        subfolder_files = await app.state.sharepoint_client.list_files(
                                            drive_id=known_working_drive_id, 
                                            token=cached_token, 
                                            folder_path=f"{sync_folder}/{subfolder_path}"
                                        )
                                        await process_items(subfolder_files, subfolder_path)
                                    except Exception as e:
                                        logger.warning(f"Background sync couldn't check subfolder {item.get('name')}: {e}")
                        
                        await process_items(files_list)

                        # Check for deletions and orphaned documents
                        current_sp_ids = set()
                        def collect_sp_ids(items):
                            for item in items:
                                if item.get("file"):
                                    current_sp_ids.add(item.get("id"))
                        
                        collect_sp_ids(files_list)

                        # Check for documents that should be deleted
                        indexed_sp_ids = {}
                        orphaned_docs = []
                        deleted_files_count = 0

                        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                            for doc_id, doc_info in app.state.index.docstore.docs.items():
                                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                                if sp_id:
                                    indexed_sp_ids[sp_id] = doc_id
                                else:
                                    file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                                    orphaned_docs.append({
                                        "doc_id": doc_id,
                                        "file_name": file_name
                                    })

                        # Find items that should be deleted
                        ids_in_index = set(indexed_sp_ids.keys())
                        ids_to_delete = ids_in_index - current_sp_ids

                        # Handle deletions (only if AUTO_DELETE_ENABLED and not requiring manual approval)
                        if ids_to_delete and settings.AUTO_DELETE_ENABLED and not settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL:
                            logger.info(f"Background sync deleting {len(ids_to_delete)} files no longer in SharePoint")
                            for sp_id in ids_to_delete:
                                try:
                                    doc_id = indexed_sp_ids[sp_id]
                                    app.state.index.delete_ref_doc(doc_id)
                                    deleted_files_count += 1
                                    logger.info(f"Background sync deleted document with SharePoint ID: {sp_id}")
                                except Exception as e:
                                    logger.error(f"Background sync deletion error for SharePoint ID {sp_id}: {e}")
                            
                            # Persist changes if we deleted anything
                            if deleted_files_count > 0:
                                app.state.index.storage_context.persist(persist_dir="storage")

                        # Log sync status with enhanced information
                        total_changes = new_files_count + len(ids_to_delete) + len(orphaned_docs)
                        
                        if settings.AUTO_IMPORT_ENABLED:
                            logger.info(f"Background sync import results: {imported_files_count} imported, {skipped_files_count} skipped, {error_files_count} errors")
                        
                        if settings.AUTO_DELETE_ENABLED and not settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL:
                            logger.info(f"Background sync deletion results: {deleted_files_count} deleted")
                        elif len(ids_to_delete) > 0:
                            logger.info(f"Background sync detected {len(ids_to_delete)} files deleted from SharePoint - manual intervention required")

                        if len(orphaned_docs) > 0:
                            logger.info(f"Background sync detected {len(orphaned_docs)} orphaned documents (no SharePoint ID) - cleanup recommended")

                        if total_changes == 0:
                            logger.info("Background sync: No changes detected - index is in sync")
                        
                        # Check if manual intervention is needed
                        manual_intervention_needed = (
                            (len(ids_to_delete) > 0 and (not settings.AUTO_DELETE_ENABLED or settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL)) or
                            len(orphaned_docs) > 0 or
                            error_files_count > 0
                        )
                        
                        # Comprehensive notification system - notify for ALL changes
                        any_changes_detected = (
                            new_files_count > 0 or 
                            imported_files_count > 0 or 
                            len(ids_to_delete) > 0 or 
                            len(orphaned_docs) > 0 or 
                            error_files_count > 0
                        )
                        
                        notification_details = {
                            "new_files_detected": new_files_count,
                            "files_imported": imported_files_count,
                            "files_skipped": skipped_files_count,
                            "import_errors": error_files_count,
                            "deleted_files_detected": len(ids_to_delete),
                            "orphaned_documents_detected": len(orphaned_docs),
                            "sync_mode": sync_mode,
                            "sync_timestamp": datetime.now().isoformat(),
                            "manual_intervention_needed": manual_intervention_needed
                        }
                        
                        # Send notifications for ALL detected changes
                        if any_changes_detected:
                            # Notification for new files detected
                            if new_files_count > 0:
                                log_admin_notification(
                                    "files_detected",
                                    f"📁 {new_files_count} new file(s) detected in SharePoint",
                                    {**notification_details, "change_type": "new_files"}
                                )
                            
                            # Notification for files successfully imported
                            if imported_files_count > 0:
                                log_admin_notification(
                                    "files_imported",
                                    f"✅ Successfully imported {imported_files_count} file(s) to search index",
                                    {**notification_details, "change_type": "import_success"}
                                )
                            
                            # Notification for files requiring manual approval/deletion
                            if len(ids_to_delete) > 0 and (not settings.AUTO_DELETE_ENABLED or settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL):
                                log_admin_notification(
                                    "deletion_approval_needed",
                                    f"🗑️ Manual approval needed for {len(ids_to_delete)} file deletion(s)",
                                    {**notification_details, "change_type": "deletion_approval"}
                                )
                            
                            # Notification for orphaned documents
                            if len(orphaned_docs) > 0:
                                log_admin_notification(
                                    "orphaned_documents",
                                    f"Found {len(orphaned_docs)} orphaned documents requiring cleanup",
                                    notification_details
                                )
                            
                            # Notification for import errors
                            if error_files_count > 0:
                                log_admin_notification(
                                    "import_errors",
                                    f"⚠️ Failed to import {error_files_count} file(s) during sync",
                                    {**notification_details, "change_type": "import_errors"}
                                )
                        
                        else:
                            # Send periodic "sync complete - no changes" notification (every 4 hours)
                            if not hasattr(app.state, 'last_no_change_notification'):
                                app.state.last_no_change_notification = datetime.now() - timedelta(hours=5)
                            
                            time_since_last_notification = datetime.now() - app.state.last_no_change_notification
                            if time_since_last_notification > timedelta(hours=4):
                                log_admin_notification(
                                    "sync_complete_no_changes",
                                    f"✅ Sync complete - SharePoint is up to date (checked {datetime.now().strftime('%I:%M %p')})",
                                    {
                                        "sync_timestamp": datetime.now().isoformat(),
                                        "sync_mode": sync_mode,
                                        "files_in_index": len(list(app.state.index.docstore.docs.keys())) if hasattr(app.state, 'index') else 0,
                                        "change_type": "no_changes"
                                    }
                                )
                                app.state.last_no_change_notification = datetime.now()
                        
                        # Send notification if backup polling was used due to webhook issues
                        if sync_mode == "backup_polling":
                            log_admin_notification(
                                "backup_polling_used",
                                f"🔄 Backup polling sync used due to webhook health issues",
                                {
                                    "sync_timestamp": datetime.now().isoformat(),
                                    "webhook_health": webhook_health,
                                    "reason": "Webhook subscriptions inactive or no recent notifications",
                                    "recommendation": "Check webhook subscription status and SharePoint connectivity"
                                }
                            )

                        # Store enhanced sync statistics for API access
                        webhook_health = check_webhook_health()
                        app.state.last_sync_stats = {
                            "status": "success",
                            "sync_mode": sync_mode,
                            "backup_polling_used": sync_mode == "backup_polling",
                            "token_available": True,
                            "new_files_detected": new_files_count,
                            "files_imported": imported_files_count,
                            "webhook_health": webhook_health,
                            "files_skipped": skipped_files_count,
                            "import_errors": error_files_count,
                            "deleted_files_detected": len(ids_to_delete),
                            "files_deleted": deleted_files_count,
                            "orphaned_documents_detected": len(orphaned_docs),
                            "manual_intervention_needed": manual_intervention_needed,
                            "sync_needed": new_files_count > imported_files_count or manual_intervention_needed,
                            "files_processed": files_processed,
                            "max_files_limit_reached": files_processed >= settings.MAX_FILES_PER_SYNC,
                            "consecutive_errors": consecutive_errors,
                            "sync_disabled": sync_disabled,
                            "last_sync_duration": (datetime.now() - sync_start_time).total_seconds(),
                            "resource_check": check_system_resources(),
                            "last_error": None
                        }
                        
                        # Reset error counter on successful sync
                        if consecutive_errors > 0:
                            logger.info(f"Background sync successful after {consecutive_errors} consecutive errors - resetting error counter")
                            consecutive_errors = 0
                            last_error_time = None
                            
                    except Exception as e:
                        logger.warning(f"Background sync check failed: {e}")
                else:
                    logger.info("No cached admin token available for background sync")
                    
            except Exception as e:
                logger.warning(f"Background sync token check failed: {e}")
            
            # Store last sync time
            app.state.last_sync_time = datetime.now()
            
        except Exception as e:
            # Enhanced error handling and recovery
            consecutive_errors += 1
            last_error_time = datetime.now()
            error_message = str(e)
            
            # Categorize error severity
            error_severity = "critical" if "token" in error_message.lower() or "auth" in error_message.lower() else "warning"
            
            logger.error(f"Error in background sync (#{consecutive_errors}): {e}", exc_info=True)
            
            # Check if we should disable sync
            if consecutive_errors >= settings.AUTO_SYNC_ERROR_THRESHOLD:
                sync_disabled = True
                logger.error(f"Background sync disabled after {consecutive_errors} consecutive errors. Will retry after {max_retry_delay} seconds.")
                
                # Send critical notification
                log_admin_notification(
                    "sync_disabled",
                    f"Background sync disabled after {consecutive_errors} consecutive errors",
                    {
                        "consecutive_errors": consecutive_errors,
                        "last_error": error_message,
                        "error_severity": error_severity,
                        "next_retry_time": (last_error_time + timedelta(seconds=max_retry_delay)).isoformat(),
                        "retry_delay_hours": max_retry_delay / 3600
                    }
                )
                
                # Store error state in stats
                app.state.last_sync_stats = {
                    "status": "disabled",
                    "consecutive_errors": consecutive_errors,
                    "sync_disabled": True,
                    "last_error": {
                        "message": error_message,
                        "severity": error_severity,
                        "timestamp": last_error_time.isoformat(),
                        "count": consecutive_errors
                    },
                    "next_retry_time": (last_error_time + timedelta(seconds=max_retry_delay)).isoformat()
                }
            else:
                # Calculate exponential backoff delay
                retry_delay = min(base_retry_delay * (2 ** (consecutive_errors - 1)), max_retry_delay)
                current_retry_delay = retry_delay
                
                logger.warning(f"Background sync will retry in {retry_delay} seconds after {consecutive_errors} consecutive errors")
                
                # Store error state in stats
                app.state.last_sync_stats = {
                    "status": "error",
                    "consecutive_errors": consecutive_errors,
                    "sync_disabled": False,
                    "last_error": {
                        "message": error_message,
                        "severity": error_severity,
                        "timestamp": last_error_time.isoformat(),
                        "count": consecutive_errors
                    },
                    "next_retry_time": (last_error_time + timedelta(seconds=retry_delay)).isoformat(),
                    "retry_delay_seconds": retry_delay
                }
        
        # Determine sleep duration (normal interval, retry delay, or exponential backoff)
        if sync_disabled:
            sleep_duration = sync_interval_seconds
        elif consecutive_errors > 0 and 'current_retry_delay' in locals():
            sleep_duration = current_retry_delay
        else:
            sleep_duration = sync_interval_seconds
            
        logger.debug(f"Background sync sleeping for {sleep_duration} seconds")
        await asyncio.sleep(sleep_duration)


def check_system_resources():
    """
    Check system resources and return status information.
    
    Returns:
        dict: Resource status information
    """
    try:
        # Check disk space for storage directory
        storage_path = Path("storage")
        storage_path.mkdir(parents=True, exist_ok=True)
        
        disk_usage = shutil.disk_usage(str(storage_path))
        free_space_gb = disk_usage.free / (1024 ** 3)
        total_space_gb = disk_usage.total / (1024 ** 3)
        used_space_gb = (disk_usage.total - disk_usage.free) / (1024 ** 3)
        disk_usage_percent = (used_space_gb / total_space_gb) * 100
        
        resource_info = {
            "disk": {
                "free_space_gb": round(free_space_gb, 2),
                "total_space_gb": round(total_space_gb, 2),
                "used_space_gb": round(used_space_gb, 2),
                "usage_percent": round(disk_usage_percent, 2),
                "low_space_warning": free_space_gb < 1.0,  # Warning if less than 1GB
                "critical_space_warning": free_space_gb < 0.5  # Critical if less than 500MB
            }
        }
        
        # Add memory info if psutil is available
        if PSUTIL_AVAILABLE:
            memory = psutil.virtual_memory()
            resource_info["memory"] = {
                "total_gb": round(memory.total / (1024 ** 3), 2),
                "available_gb": round(memory.available / (1024 ** 3), 2),
                "used_gb": round(memory.used / (1024 ** 3), 2),
                "usage_percent": memory.percent,
                "high_usage_warning": memory.percent > 80,  # Warning if over 80%
                "critical_usage_warning": memory.percent > 90  # Critical if over 90%
            }
            
            # Add process-specific memory info
            try:
                process = psutil.Process()
                process_memory = process.memory_info()
                resource_info["process"] = {
                    "memory_mb": round(process_memory.rss / (1024 ** 2), 2),
                    "cpu_percent": process.cpu_percent(),
                    "num_threads": process.num_threads()
                }
            except Exception as e:
                logger.warning(f"Could not get process information: {e}")
        
        return resource_info
        
    except Exception as e:
        logger.error(f"Error checking system resources: {e}")
        return {
            "error": f"Failed to check resources: {str(e)}",
            "disk": {"critical_space_warning": True},  # Assume critical to be safe
            "memory": {"critical_usage_warning": False}
        }


def should_skip_sync_due_to_resources():
    """
    Check if sync should be skipped due to resource constraints.
    
    Returns:
        tuple: (should_skip: bool, reason: str)
    """
    try:
        resources = check_system_resources()
        
        # Check disk space
        if resources.get("disk", {}).get("critical_space_warning", False):
            return True, f"Critical disk space: {resources['disk'].get('free_space_gb', 0):.2f}GB remaining"
        
        # Check memory if available
        if PSUTIL_AVAILABLE and resources.get("memory", {}).get("critical_usage_warning", False):
            return True, f"Critical memory usage: {resources['memory'].get('usage_percent', 0):.1f}%"
        
        return False, ""
        
    except Exception as e:
        logger.error(f"Error checking resource constraints: {e}")
        # Be conservative - skip sync if we can't check resources
        return True, f"Resource check failed: {str(e)}"


def _log_to_sidebar_only(notification_type: str, message: str, details: dict = None):
    """
    Internal function to log directly to sidebar without triggering multi-channel notifications.
    This prevents circular dependency issues.
    
    Args:
        notification_type: Type of notification
        message: Human-readable notification message
        details: Additional details about the notification
    """
    try:
        notifications_dir = Path("storage/data/notifications")
        notifications_dir.mkdir(parents=True, exist_ok=True)
        
        notification = {
            "timestamp": datetime.now().isoformat(),
            "type": notification_type,
            "message": message,
            "details": details or {},
            "acknowledged": False
        }
        
        # Save to daily notification file
        date_str = datetime.now().strftime("%Y-%m-%d")
        notification_file = notifications_dir / f"notifications_{date_str}.json"
        
        # Load existing notifications or create new list
        notifications = []
        if notification_file.exists():
            try:
                with open(notification_file, 'r') as f:
                    notifications = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load existing notifications: {e}")
                notifications = []
        
        # Add new notification
        notifications.append(notification)
        
        # Keep only last 100 notifications per day
        notifications = notifications[-100:]
        
        # Save updated notifications
        with open(notification_file, 'w') as f:
            json.dump(notifications, f, indent=2)
        
        # Also log to application logger
        logger.warning(f"ADMIN NOTIFICATION [{notification_type}]: {message}")
        
    except Exception as e:
        logger.error(f"Failed to log sidebar notification: {e}")


def log_admin_notification(notification_type: str, message: str, details: dict = None):
    """
    Log an admin notification for manual intervention needs.
    
    Args:
        notification_type: Type of notification (sync_needed, error, resource_warning, etc.)
        message: Human-readable notification message
        details: Additional details about the notification
    """
    try:
        # Create notification object
        notification = {
            "timestamp": datetime.now().isoformat(),
            "type": notification_type,
            "message": message,
            "details": details or {},
            "acknowledged": False
        }
        
        # Always log to sidebar first (prevents circular dependency)
        _log_to_sidebar_only(notification_type, message, details)
        
        # Send multi-channel notification if configured (excluding sidebar since it's already done)
        if settings.AUTO_SYNC_NOTIFICATION_EMAIL or settings.EMAIL_ENABLED:
            # Determine notification priority and channels
            priority = get_notification_priority(notification_type)
            
            # Select channels based on priority (sidebar excluded since already handled)
            if priority == 'critical':
                channels = ['email', 'webhook']
            elif priority == 'high':
                channels = ['email']
            else:
                channels = []  # Low/medium priority already handled by sidebar
            
            # Send via multi-channel system if there are additional channels to notify
            if channels:
                send_multi_channel_notification(notification, channels)
        
    except Exception as e:
        logger.error(f"Failed to log admin notification: {e}")


def send_multi_channel_notification(notification: dict, channels: list = None, retry_count: int = 0):
    """
    Enhanced multi-channel notification with retry logic and delivery verification.
    
    Args:
        notification: Notification details
        channels: List of channels to send to ['email', 'sidebar', 'webhook'] (defaults to all)
        retry_count: Current retry attempt (for internal use)
    
    Returns:
        dict: Status of each channel delivery
    """
    if channels is None:
        channels = ['email', 'sidebar']
    
    results = {
        'email': {'sent': False, 'error': None},
        'sidebar': {'sent': False, 'error': None},
        'webhook': {'sent': False, 'error': None}
    }
    
    # Send to sidebar first (always works)
    if 'sidebar' in channels:
        try:
            _log_to_sidebar_only(
                notification.get('type', 'notification'),
                notification.get('message', 'No message'),
                notification.get('details', {})
            )
            results['sidebar']['sent'] = True
            logger.info("Sidebar notification sent successfully")
        except Exception as e:
            results['sidebar']['error'] = str(e)
            logger.error(f"Failed to send sidebar notification: {e}")
    
    # Send email with retry logic
    if 'email' in channels:
        email_result = send_email_notification(notification, retry_count)
        results['email']['sent'] = email_result
        if not email_result:
            results['email']['error'] = "Email delivery failed"
    
    # Send webhook notification if configured
    if 'webhook' in channels and settings.WEBHOOK_NOTIFICATION_URL:
        try:
            webhook_result = send_webhook_notification(notification)
            results['webhook']['sent'] = webhook_result
            if not webhook_result:
                results['webhook']['error'] = "Webhook delivery failed"
        except Exception as e:
            results['webhook']['error'] = str(e)
            logger.error(f"Failed to send webhook notification: {e}")
    
    return results


def send_email_notification(notification: dict, retry_count: int = 0):
    """
    Enhanced email notification with retry logic and delivery verification.
    
    Args:
        notification: Notification details
        retry_count: Current retry attempt (for internal use)
    """
    try:
        # Check if email is enabled and configured
        if not settings.EMAIL_ENABLED:
            logger.info("Email notifications are disabled")
            return False
            
        if not settings.AUTO_SYNC_NOTIFICATION_EMAIL:
            logger.warning("No notification email configured")
            return False
            
        if not settings.SMTP_EMAIL or not settings.SMTP_PASSWORD:
            logger.warning("SMTP credentials not configured")
            return False
        
        # Parse multiple email addresses
        email_addresses = [email.strip() for email in settings.AUTO_SYNC_NOTIFICATION_EMAIL.split(',')]
        email_addresses = [email for email in email_addresses if email]  # Remove empty strings
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = settings.SMTP_EMAIL
        msg['To'] = ', '.join(email_addresses)
        
        # Create user-friendly email body
        notification_type = notification.get('type', 'Unknown')
        message = notification.get('message', 'No message')
        timestamp = notification.get('timestamp', datetime.now().isoformat())
        details = notification.get('details', {})
        
        # Format timestamp to be more readable
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            readable_time = dt.strftime('%B %d, %Y at %I:%M %p')
        except:
            readable_time = timestamp
        
        # Create user-friendly subject and body based on notification type
        if notification_type == 'test':
            subject_type = "Test Email"
            body = f"""
# 📧 DDBrain Test Email

Hello Admin,

This is a **test email** to confirm that DDBrain email notifications are working correctly.

## 📋 Test Details
- **Sent on:** {readable_time}
- **Test initiated by:** {details.get('test_sent_by', 'Unknown admin')}
- **System:** DDBrain SharePoint Document System

## ✅ What This Means
If you received this email, your notification system is working perfectly! You will receive important alerts about:
- Document sync issues
- System status changes  
- Important administrative updates

---
*This is an automated message from DDBrain. No action required.*
"""
        elif notification_type == 'settings_updated':
            subject_type = "Settings Changed"
            changes = details.get('changes', [])
            changes_list = '\n'.join([f"- {change}" for change in changes])
            
            body = f"""
# ⚙️ DDBrain Settings Updated

Hello Admin,

Your DDBrain system settings have been successfully updated.

## 📋 What Changed
{changes_list if changes_list else '- Settings were modified'}

## 📅 When
**Updated on:** {readable_time}

## 👤 Who Made Changes
**Changed by:** {details.get('updated_by', 'Unknown admin')}

## ✅ What This Means
Your changes have been applied and are now active. The system will use these new settings for all future operations.

---
*This is an automated notification from DDBrain. No action required unless there's an issue.*
"""
        elif notification_type == 'sync_error':
            subject_type = "Sync Problem"
            body = f"""
# ⚠️ DDBrain Sync Issue

Hello Admin,

There's an issue with your document synchronization that needs attention.

## 🚨 Problem
{message}

## 📅 When
**Occurred on:** {readable_time}

## 🔧 What You Should Do
1. Check your SharePoint connection
2. Verify your login credentials are working
3. Review the Admin Dashboard for more details
4. Contact IT support if the problem persists

## 📞 Need Help?
If you're unsure how to fix this, please contact your technical support team.

---
*This is an important alert from DDBrain. Please review and take action if needed.*
"""
        else:
            subject_type = "System Alert"
            body = f"""
# 🔔 DDBrain Notification

Hello Admin,

## 📋 Alert Details
**Type:** {notification_type.replace('_', ' ').title()}
**Message:** {message}
**Time:** {readable_time}

## ℹ️ Additional Information
{json.dumps(details, indent=2) if details else 'No additional details available.'}

---
*This is an automated notification from DDBrain SharePoint Document System.*
"""
        
        # Update subject line to be more descriptive
        msg['Subject'] = f"DDBrain Alert: {subject_type}"
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        with smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_EMAIL, settings.SMTP_PASSWORD)
            server.send_message(msg)
        
        logger.info(f"✅ Email notification sent successfully to {len(email_addresses)} recipient(s): {', '.join(email_addresses)}")
        
        # Log successful delivery for audit trail
        log_email_delivery_status(notification, email_addresses, "success", retry_count)
        return True
        
    except Exception as e:
        error_msg = f"Failed to send email notification (attempt {retry_count + 1}): {e}"
        logger.error(error_msg)
        
        # Retry logic for temporary failures
        max_retries = 3
        retry_delay_minutes = [2, 5, 15]  # Progressive delays
        
        if retry_count < max_retries:
            # Check if it's a retryable error
            retryable_errors = [
                "Connection refused", "timeout", "Network is unreachable", 
                "Temporary failure", "450", "451", "452"
            ]
            
            if any(error_text in str(e).lower() for error_text in retryable_errors):
                delay_minutes = retry_delay_minutes[min(retry_count, len(retry_delay_minutes) - 1)]
                logger.warning(f"🔄 Retrying email notification in {delay_minutes} minutes (attempt {retry_count + 2}/{max_retries + 1})")
                
                # Schedule retry (in a real implementation, you'd use a proper task queue)
                import threading
                import time
                
                def delayed_retry():
                    time.sleep(delay_minutes * 60)
                    send_email_notification(notification, retry_count + 1)
                
                retry_thread = threading.Thread(target=delayed_retry)
                retry_thread.daemon = True
                retry_thread.start()
                
                # Log retry attempt
                log_email_delivery_status(notification, [], "retry_scheduled", retry_count, str(e))
                return False
            else:
                logger.error(f"❌ Non-retryable email error: {e}")
        
        # Log final failure
        log_email_delivery_status(notification, [], "failed", retry_count, str(e))
        
        # Try backup notification method
        try_backup_notification(notification, error_msg)
        return False


def log_email_delivery_status(notification: dict, recipients: list, status: str, retry_count: int, error: str = None):
    """Log email delivery status for audit trail."""
    try:
        delivery_log = {
            "timestamp": datetime.now().isoformat(),
            "notification_type": notification.get('type', ''),
            "recipients": recipients,
            "status": status,
            "retry_count": retry_count,
            "error": error
        }
        
        # Store in delivery log file
        log_dir = Path("storage/data/email_delivery")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"delivery_log_{datetime.now().strftime('%Y-%m-%d')}.json"
        
        if log_file.exists():
            with open(log_file, 'r') as f:
                logs = json.load(f)
        else:
            logs = []
        
        logs.append(delivery_log)
        
        with open(log_file, 'w') as f:
            json.dump(logs, f, indent=2)
            
        logger.debug(f"Email delivery status logged: {status}")
        
    except Exception as e:
        logger.warning(f"Failed to log email delivery status: {e}")


def try_backup_notification(notification: dict, primary_error: str):
    """Try backup notification methods when email fails."""
    try:
        # Backup 1: Enhanced in-app notification with failure notice
        enhanced_notification = {
            **notification,
            "type": f"{notification.get('type', '')}_email_failed",
            "message": f"📧 EMAIL DELIVERY FAILED: {notification.get('message', '')}",
            "details": {
                **notification.get('details', {}),
                "email_error": primary_error,
                "fallback_notification": True,
                "action_required": True
            }
        }
        
        log_admin_notification(
            enhanced_notification['type'],
            enhanced_notification['message'],
            enhanced_notification['details']
        )
        
        # Backup 2: Write to emergency notification file
        emergency_dir = Path("storage/data/emergency_notifications")
        emergency_dir.mkdir(parents=True, exist_ok=True)
        
        emergency_file = emergency_dir / f"failed_emails_{datetime.now().strftime('%Y-%m-%d')}.txt"
        
        with open(emergency_file, 'a') as f:
            f.write(f"\n[{datetime.now().isoformat()}] EMAIL DELIVERY FAILED\n")
            f.write(f"Type: {notification.get('type', '')}\n")
            f.write(f"Message: {notification.get('message', '')}\n")
            f.write(f"Error: {primary_error}\n")
            f.write("-" * 50 + "\n")
        
        logger.info(f"✅ Backup notifications created for failed email: {notification.get('type', '')}")
        
    except Exception as e:
        logger.error(f"❌ Backup notification methods also failed: {e}")


def send_webhook_notification(notification: dict, retry_count: int = 0) -> bool:
    """
    Send notification via webhook to external systems.
    
    Args:
        notification: Notification details
        retry_count: Current retry attempt
    
    Returns:
        bool: True if sent successfully, False otherwise
    """
    try:
        import aiohttp
        import asyncio
        
        # Check if webhook URL is configured
        webhook_url = getattr(settings, 'NOTIFICATION_WEBHOOK_URL', None)
        if not webhook_url:
            logger.debug("No webhook URL configured for notifications")
            return False
        
        # Prepare webhook payload
        webhook_payload = {
            "timestamp": datetime.now().isoformat(),
            "source": "ddbrain_sharepoint",
            "notification_type": notification.get('type', 'unknown'),
            "message": notification.get('message', ''),
            "details": notification.get('details', {}),
            "priority": get_notification_priority(notification.get('type', '')),
            "retry_count": retry_count
        }
        
        async def send_webhook():
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    webhook_url,
                    json=webhook_payload,
                    headers={
                        "Content-Type": "application/json",
                        "X-Source": "DDBrain-SharePoint",
                        "X-Notification-Type": notification.get('type', 'unknown')
                    }
                ) as response:
                    if response.status in [200, 201, 202]:
                        logger.info(f"✅ Webhook notification sent successfully to {webhook_url}")
                        return True
                    else:
                        logger.warning(f"Webhook returned status {response.status}: {await response.text()}")
                        return False
        
        # Run async function
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If already in async context, create task
                task = asyncio.create_task(send_webhook())
                # Note: This won't wait for completion in the current context
                return True  # Assume success for now
            else:
                # If not in async context, run directly
                return loop.run_until_complete(send_webhook())
        except RuntimeError:
            # Fallback for cases where event loop handling is complex
            logger.warning("Could not send webhook due to event loop constraints")
            return False
            
    except Exception as e:
        logger.error(f"Failed to send webhook notification (attempt {retry_count + 1}): {e}")
        
        # Retry logic for webhooks
        max_retries = 2
        if retry_count < max_retries:
            retry_delay = 5 * (retry_count + 1)  # 5, 10 seconds
            logger.info(f"🔄 Retrying webhook notification in {retry_delay} seconds")
            
            import threading
            import time
            
            def delayed_webhook_retry():
                time.sleep(retry_delay)
                send_webhook_notification(notification, retry_count + 1)
            
            retry_thread = threading.Thread(target=delayed_webhook_retry)
            retry_thread.daemon = True
            retry_thread.start()
        
        return False


def get_notification_priority(notification_type: str) -> str:
    """
    Determine notification priority based on type.
    
    Args:
        notification_type: Type of notification
    
    Returns:
        str: Priority level (critical, high, medium, low)
    """
    critical_types = ['sync_error', 'system_failure', 'security_alert']
    high_types = [
        'webhook_subscription_failed', 'token_validation_failed', 'backup_sync_failed',
        'test', 'settings_updated', 'notification_system_test', 'email_failed',
        'import_errors', 'orphaned_documents'
    ]
    medium_types = ['webhook_subscription_created', 'sync_completed', 'webhook_change_detected']
    
    if notification_type in critical_types:
        return 'critical'
    elif notification_type in high_types:
        return 'high'
    elif notification_type in medium_types:
        return 'medium'
    else:
        return 'low'


def get_recent_notifications(days: int = 7, unacknowledged_only: bool = False) -> List[dict]:
    """
    Get recent admin notifications.
    
    Args:
        days: Number of days to look back
        unacknowledged_only: Only return unacknowledged notifications
        
    Returns:
        List of notification dictionaries
    """
    try:
        notifications_dir = Path("storage/data/notifications")
        if not notifications_dir.exists():
            return []
        
        all_notifications = []
        
        # Look through recent notification files
        for i in range(days):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            notification_file = notifications_dir / f"notifications_{date_str}.json"
            
            if notification_file.exists():
                try:
                    with open(notification_file, 'r') as f:
                        daily_notifications = json.load(f)
                        all_notifications.extend(daily_notifications)
                except Exception as e:
                    logger.warning(f"Could not load notifications from {notification_file}: {e}")
        
        # Filter by acknowledgment status if requested
        if unacknowledged_only:
            all_notifications = [n for n in all_notifications if not n.get("acknowledged", False)]
        
        # Sort by timestamp (newest first)
        all_notifications.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return all_notifications[:100]  # Limit to 100 most recent
        
    except Exception as e:
        logger.error(f"Failed to get recent notifications: {e}")
        return []


def validate_model_name(model_name):
    """Validate that the model name is safe and doesn't contain URL parameters or invalid characters."""
    if not model_name or not isinstance(model_name, str):
        raise ValueError(f"Invalid model name: {model_name}")
    
    # Check for URL parameter contamination
    invalid_patterns = ['=', '&', '?', ':', 'login_success', 'http', 'https']
    for pattern in invalid_patterns:
        if pattern in model_name:
            raise ValueError(f"Model name contains invalid pattern '{pattern}': {model_name}")
    
    # Check that it's a reasonable model name
    if len(model_name) < 3 or len(model_name) > 50:
        raise ValueError(f"Model name has invalid length: {model_name}")
    
    return True


def create_model_instances():
    """Create isolated LLM and embedding model instances to avoid global state corruption."""
    try:
        # Validate model name before creating instances
        validate_model_name(settings.OPENAI_MODEL)
        logger.info(f"Creating model instances with validated model: {settings.OPENAI_MODEL}")
        
        llm = LlamaOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.LLM_TEMPERATURE,
            max_tokens=settings.LLM_MAX_TOKENS,
        )
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        logger.info("Created isolated model instances successfully")
        return llm, embed_model
    except Exception as e:
        logger.error(f"Error creating model instances: {str(e)}")
        raise


async def create_empty_index():
    """Create an empty index without documents using isolated model instances."""
    try:
        logger.info("Creating new empty index...")
        # Create isolated model instances to avoid global state corruption
        llm, embed_model = create_model_instances()
        
        # Temporarily set global settings for index creation, then restore
        original_llm = getattr(LlamaSettings, 'llm', None)
        original_embed_model = getattr(LlamaSettings, 'embed_model', None)
        
        try:
            LlamaSettings.llm = llm
            LlamaSettings.embed_model = embed_model
            
            # Create an empty vector store index
            index = VectorStoreIndex([])
            
            # Persist the empty index to storage
            storage_context = index.storage_context
            storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
            logger.info("Successfully created and persisted empty index")
            
            return index
        finally:
            # Restore original global settings to prevent contamination
            if original_llm is not None:
                LlamaSettings.llm = original_llm
            elif hasattr(LlamaSettings, 'llm'):
                delattr(LlamaSettings, 'llm')
            
            if original_embed_model is not None:
                LlamaSettings.embed_model = original_embed_model
            elif hasattr(LlamaSettings, 'embed_model'):
                delattr(LlamaSettings, 'embed_model')
                
    except Exception as e:
        logger.error(f"Error creating empty index: {str(e)}")
        # Return a non-persisted index as fallback
        logger.warning("Creating non-persisted fallback index")
        return VectorStoreIndex([])


def get_query_engine(index, reranker=None):
    """Create a query engine from the index and reranker."""
    try:
        logger.info("Setting up query engine...")
        # Create retriever using config similarity_top_k setting
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=settings.SIMILARITY_TOP_K,
        )
        logger.info("Created vector index retriever")

        # Create response synthesizer with more detailed configuration
        response_synthesizer = get_response_synthesizer(
            response_mode="compact",
            verbose=True,
            streaming=True,
        )
        logger.info("Created response synthesizer")

        # Create similarity threshold postprocessor
        # Only keep documents with similarity score >= threshold
        similarity_postprocessor = SimilarityPostprocessor(
            similarity_cutoff=settings.SIMILARITY_THRESHOLD
        )
        logger.info(f"Created similarity postprocessor with threshold {settings.SIMILARITY_THRESHOLD}")

        # Build list of postprocessors
        postprocessors = [similarity_postprocessor]
        if reranker:
            postprocessors.append(reranker)

        # Create query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=postprocessors,
        )
        logger.info("Query engine created successfully")
        return query_engine
    except Exception as e:
        logger.error(f"Error creating query engine: {str(e)}")
        return None


# Function to load or create the vector index
async def load_index():
    """Load the vector index from storage or create a new one if it doesn't exist."""
    logger.info("Attempting to load index from local storage...")
    storage_dir = settings.STORAGE_DIR
    try:
        if storage_dir.exists() and any(storage_dir.iterdir()):
            storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
            index = load_index_from_storage(storage_context)
            logger.info("Index loaded successfully from storage")
            return index
        else:
            logger.warning("No existing index found; creating empty index")
            return await create_empty_index()
    except Exception as e:
        logger.error(f"Error loading index: {e}", exc_info=True)
        logger.warning("Falling back to creating empty index")
        return await create_empty_index()


async def persist_index():
    """Persists the current index state to disk."""
    if hasattr(app.state, "index") and hasattr(app.state.index, "storage_context"):
        try:
            logger.info("Persisting index changes to storage...")
            # Run the potentially blocking I/O in a separate thread
            await asyncio.to_thread(
                app.state.index.storage_context.persist,
                persist_dir=str(settings.STORAGE_DIR),
            )
            logger.info("Index changes persisted successfully.")
        except Exception as e:
            logger.error(f"Error persisting index changes: {e}", exc_info=True)
    else:
        logger.warning("Index or storage context not available, cannot persist.")


async def verify_deletion(doc_id: str) -> bool:
    """Verify that a document has been completely removed from all storage components."""
    try:
        if not hasattr(app.state, "index") or not app.state.index:
            logger.warning(f"Index not available for deletion verification of {doc_id}")
            return False
            
        deletion_verified = True
        
        # Check if document exists in docstore
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            if doc_id in app.state.index.docstore.docs:
                logger.warning(f"VERIFICATION FAILED: Document {doc_id} still exists in docstore after deletion")
                deletion_verified = False
            else:
                logger.info(f"✓ Document {doc_id} successfully removed from docstore")
        
        # For SimpleVectorStore, check if we can still retrieve nodes by doc_id
        if hasattr(app.state.index, "vector_store"):
            try:
                # Check if any nodes still reference this document
                all_nodes = await asyncio.to_thread(getattr, app.state.index.vector_store, '_data', {})
                nodes_with_doc_id = []
                
                if hasattr(all_nodes, 'values'):
                    for node in all_nodes.values():
                        if hasattr(node, 'ref_doc_id') and node.ref_doc_id == doc_id:
                            nodes_with_doc_id.append(node)
                
                if nodes_with_doc_id:
                    logger.warning(f"VERIFICATION FAILED: Document {doc_id} still has {len(nodes_with_doc_id)} nodes in vector store")
                    deletion_verified = False
                else:
                    logger.info(f"✓ Document {doc_id} has no remaining nodes in vector store")
                    
            except Exception as vector_check_error:
                # If we can't check the vector store, assume it's okay
                logger.info(f"Cannot verify vector store deletion for {doc_id}: {vector_check_error}")
        
        # Additional check: verify document is not in the index structure
        if hasattr(app.state.index, 'index_struct') and hasattr(app.state.index.index_struct, 'nodes_dict'):
            if doc_id in app.state.index.index_struct.nodes_dict:
                logger.warning(f"VERIFICATION FAILED: Document {doc_id} still in index structure")
                deletion_verified = False
            else:
                logger.info(f"✓ Document {doc_id} removed from index structure")
        
        if deletion_verified:
            logger.info(f"✅ Document {doc_id} deletion VERIFIED across all storage components")
        else:
            logger.error(f"❌ Document {doc_id} deletion FAILED verification - document still exists")
            
        return deletion_verified
        
    except Exception as e:
        logger.error(f"Error verifying deletion of {doc_id}: {e}", exc_info=True)
        return False


async def delete_sharepoint_document_completely(sharepoint_id: str) -> bool:
    """
    Delete ALL nodes belonging to a SharePoint document using robust deletion methods.
    This finds all reference document IDs for the SharePoint document and deletes them properly.
    
    Args:
        sharepoint_id: SharePoint ID of the document to delete completely
        
    Returns:
        bool: True if all document references were successfully deleted
    """
    # MASSIVE LOGGING FOR DEBUGGING
    logger.error(f"🔥🔥🔥 DELETE_SHAREPOINT_DOCUMENT_COMPLETELY CALLED! SharePoint ID: {sharepoint_id}")
    logger.error(f"🔍 Function is being executed - our changes DID take effect!")
    
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("❌ Index not available for SharePoint document deletion")
        return False
    
    try:
        logger.error(f"🚀 Starting complete deletion of SharePoint document {sharepoint_id}")
        
        # Count total documents before
        total_docs_before = len(app.state.index.docstore.docs) if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs") else 0
        logger.error(f"📊 Total documents BEFORE deletion: {total_docs_before}")
        
        # Find ALL unique reference document IDs that belong to this SharePoint document
        ref_docs_to_delete = set()  # Use set to avoid duplicates
        file_name_for_logging = "Unknown"
        nodes_found = []
        
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for node_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                if sp_id == sharepoint_id:
                    # Get the reference document ID
                    ref_doc_id = doc_info.ref_doc_id if hasattr(doc_info, 'ref_doc_id') else None
                    if ref_doc_id:
                        ref_docs_to_delete.add(ref_doc_id)
                        file_name_for_logging = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "Unknown"
                        nodes_found.append({
                            "node_id": node_id,
                            "ref_doc_id": ref_doc_id,
                            "file_name": file_name_for_logging
                        })
        
        logger.error(f"🔍 Found {len(nodes_found)} nodes for SharePoint ID {sharepoint_id}:")
        for node in nodes_found:
            logger.error(f"   📄 Node {node['node_id'][:8]}... -> Ref Doc {node['ref_doc_id']} ({node['file_name']})")
        
        if not ref_docs_to_delete:
            logger.error(f"⚠️ No reference documents found for SharePoint document {sharepoint_id}")
            return True
        
        logger.error(f"🎯 Found {len(ref_docs_to_delete)} reference documents to delete for SharePoint document {sharepoint_id} ({file_name_for_logging})")
        for ref_doc_id in ref_docs_to_delete:
            logger.error(f"   🗂️ Reference Document ID: {ref_doc_id}")
        
        # Delete each reference document using LlamaIndex's built-in method
        successfully_deleted = []
        failed_to_delete = []
        
        logger.error(f"🔥 Starting deletion of {len(ref_docs_to_delete)} reference documents...")
        
        for ref_doc_id in ref_docs_to_delete:
            try:
                logger.error(f"🗑️ Deleting reference document {ref_doc_id}...")
                
                # Use LlamaIndex's delete_ref_doc method with complete removal
                logger.error(f"📞 Calling app.state.index.delete_ref_doc({ref_doc_id}, delete_from_docstore=True)")
                await asyncio.to_thread(
                    app.state.index.delete_ref_doc,
                    ref_doc_id,
                    delete_from_docstore=True  # Critical: Ensures complete removal from storage
                )
                logger.error(f"✅ delete_ref_doc call completed for {ref_doc_id}")
                
                # Verify the deletion worked by checking if nodes still exist
                nodes_still_exist = []
                if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                    for node_id, doc_info in app.state.index.docstore.docs.items():
                        if hasattr(doc_info, 'ref_doc_id') and doc_info.ref_doc_id == ref_doc_id:
                            nodes_still_exist.append(node_id)
                
                if nodes_still_exist:
                    logger.error(f"❌ {len(nodes_still_exist)} nodes still exist for ref_doc_id {ref_doc_id}")
                    for node_id in nodes_still_exist:
                        logger.error(f"   👻 Zombie node: {node_id}")
                    failed_to_delete.append(ref_doc_id)
                else:
                    logger.error(f"✅ Successfully deleted reference document {ref_doc_id}")
                    successfully_deleted.append(ref_doc_id)
                    
            except KeyError as ke:
                logger.error(f"🔧 LlamaIndex KeyError bug detected for {ref_doc_id}: {ke}")
                logger.error(f"🛠️ IMPLEMENTING MANUAL CLEANUP for orphaned reference...")
                
                # Manual cleanup when LlamaIndex fails due to internal inconsistency
                manual_cleanup_success = True
                nodes_removed = 0
                
                # Remove all nodes with this ref_doc_id from docstore
                if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                    nodes_to_remove = []
                    for node_id, doc_info in app.state.index.docstore.docs.items():
                        if hasattr(doc_info, 'ref_doc_id') and doc_info.ref_doc_id == ref_doc_id:
                            nodes_to_remove.append(node_id)
                    
                    for node_id in nodes_to_remove:
                        try:
                            del app.state.index.docstore.docs[node_id]
                            nodes_removed += 1
                            logger.error(f"🗑️ Manually removed node {node_id} from docstore")
                        except Exception as e:
                            logger.error(f"❌ Failed to manually remove node {node_id}: {e}")
                            manual_cleanup_success = False
                
                # Remove from vector store using multiple approaches
                vector_nodes_removed = 0
                nodes_for_ref_doc = [n['node_id'] for n in nodes_found if n['ref_doc_id'] == ref_doc_id]
                
                # Try direct vector store access
                if hasattr(app.state.index, "vector_store"):
                    vector_store = app.state.index.vector_store
                    
                    # For SimpleVectorStore with embedding_dict
                    if hasattr(vector_store, '_data') and hasattr(vector_store._data, 'embedding_dict'):
                        for node_id in nodes_for_ref_doc:
                            if node_id in vector_store._data.embedding_dict:
                                try:
                                    del vector_store._data.embedding_dict[node_id]
                                    vector_nodes_removed += 1
                                    logger.error(f"🗑️ Manually removed node {node_id} from vector store embedding_dict")
                                except Exception as e:
                                    logger.error(f"❌ Failed to remove node {node_id} from vector store: {e}")
                    
                    # Alternative: direct embedding_dict access
                    elif hasattr(vector_store, 'embedding_dict'):
                        for node_id in nodes_for_ref_doc:
                            if node_id in vector_store.embedding_dict:
                                try:
                                    del vector_store.embedding_dict[node_id]
                                    vector_nodes_removed += 1
                                    logger.error(f"🗑️ Manually removed node {node_id} from vector store")
                                except Exception as e:
                                    logger.error(f"❌ Failed to remove node {node_id} from vector store: {e}")
                
                logger.error(f"🗑️ Removed {vector_nodes_removed} nodes from vector store")
                
                # Remove from index structure if it exists
                index_nodes_removed = 0
                if hasattr(app.state.index, "index_struct") and hasattr(app.state.index.index_struct, "nodes_dict"):
                    for node_id in nodes_for_ref_doc:
                        if node_id in app.state.index.index_struct.nodes_dict:
                            try:
                                del app.state.index.index_struct.nodes_dict[node_id]
                                index_nodes_removed += 1
                                logger.error(f"🗑️ Manually removed node {node_id} from index structure")
                            except Exception as e:
                                logger.error(f"❌ Failed to remove node {node_id} from index structure: {e}")
                
                logger.error(f"🗑️ Removed {index_nodes_removed} nodes from index structure")
                
                # Also remove the reference document itself from docstore metadata
                if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                    if ref_doc_id in app.state.index.docstore.docs:
                        try:
                            del app.state.index.docstore.docs[ref_doc_id]
                            logger.error(f"🗑️ Manually removed ref_doc {ref_doc_id} from docstore")
                        except Exception as e:
                            logger.error(f"❌ Failed to remove ref_doc {ref_doc_id} from docstore: {e}")
                
                # CRITICAL: Direct storage file modification (bypass LlamaIndex persistence)
                try:
                    logger.error(f"💾 IMPLEMENTING DIRECT STORAGE FILE MODIFICATION")
                    
                    # Directly modify docstore.json to remove the ref_doc_id and nodes
                    import json
                    from pathlib import Path
                    
                    docstore_path = Path("storage/docstore.json")
                    if docstore_path.exists():
                        logger.error(f"📂 Reading docstore.json...")
                        with open(docstore_path, 'r') as f:
                            docstore_data = json.load(f)
                        
                        # Remove the reference document from metadata
                        if "docstore/metadata" in docstore_data:
                            if ref_doc_id in docstore_data["docstore/metadata"]:
                                del docstore_data["docstore/metadata"][ref_doc_id]
                                logger.error(f"🗑️ Removed ref_doc {ref_doc_id} from docstore metadata")
                        
                        # Remove the nodes from docstore data
                        nodes_removed_from_file = 0
                        if "docstore/data" in docstore_data:
                            for node_id in nodes_for_ref_doc:
                                if node_id in docstore_data["docstore/data"]:
                                    del docstore_data["docstore/data"][node_id]
                                    nodes_removed_from_file += 1
                                    logger.error(f"🗑️ Removed node {node_id} from docstore file")
                        
                        # Write back to file
                        with open(docstore_path, 'w') as f:
                            json.dump(docstore_data, f)
                        logger.error(f"💾 DIRECT FILE MODIFICATION: removed {nodes_removed_from_file} nodes from docstore.json")
                    
                    # CRITICAL: Also remove from index_store.json (this contains the vector store nodes_dict)
                    index_store_path = Path("storage/index_store.json")
                    if index_store_path.exists():
                        logger.error(f"📂 Reading index_store.json for vector store cleanup...")
                        with open(index_store_path, 'r') as f:
                            index_store_data = json.load(f)
                        
                        # Navigate to the vector store nodes_dict
                        index_nodes_removed_from_file = 0
                        if "index_store/data" in index_store_data:
                            for index_id, index_info in index_store_data["index_store/data"].items():
                                if index_info.get("__type__") == "vector_store" and "__data__" in index_info:
                                    # Parse the JSON string in __data__
                                    try:
                                        vector_store_data = json.loads(index_info["__data__"])
                                        if "nodes_dict" in vector_store_data:
                                            # Remove our nodes from the nodes_dict
                                            for node_id in nodes_for_ref_doc:
                                                if node_id in vector_store_data["nodes_dict"]:
                                                    del vector_store_data["nodes_dict"][node_id]
                                                    index_nodes_removed_from_file += 1
                                                    logger.error(f"🗑️ Removed node {node_id} from index_store vector store")
                                            
                                            # Update the __data__ with the modified vector store data
                                            index_info["__data__"] = json.dumps(vector_store_data)
                                    except json.JSONDecodeError as e:
                                        logger.error(f"❌ Failed to parse vector store data: {e}")
                        
                        # Write back to file
                        with open(index_store_path, 'w') as f:
                            json.dump(index_store_data, f)
                        logger.error(f"💾 VECTOR STORE CLEANUP: removed {index_nodes_removed_from_file} nodes from index_store.json")
                    
                    # CRITICAL: Also remove from vector_store.json if it exists
                    vector_store_path = Path("storage/vector_store.json")
                    if vector_store_path.exists():
                        logger.error(f"📂 Reading vector_store.json for embedding cleanup...")
                        with open(vector_store_path, 'r') as f:
                            vector_store_data = json.load(f)
                        
                        vector_nodes_removed_from_file = 0
                        # Remove from embedding_dict
                        if "embedding_dict" in vector_store_data:
                            for node_id in nodes_for_ref_doc:
                                if node_id in vector_store_data["embedding_dict"]:
                                    del vector_store_data["embedding_dict"][node_id]
                                    vector_nodes_removed_from_file += 1
                                    logger.error(f"🗑️ Removed node {node_id} from vector_store embedding_dict")
                        
                        # Remove from text_id_to_ref_doc_id
                        if "text_id_to_ref_doc_id" in vector_store_data:
                            for node_id in nodes_for_ref_doc:
                                if node_id in vector_store_data["text_id_to_ref_doc_id"]:
                                    del vector_store_data["text_id_to_ref_doc_id"][node_id]
                                    logger.error(f"🗑️ Removed node {node_id} from text_id_to_ref_doc_id")
                        
                        # Remove from metadata_dict
                        if "metadata_dict" in vector_store_data:
                            for node_id in nodes_for_ref_doc:
                                if node_id in vector_store_data["metadata_dict"]:
                                    del vector_store_data["metadata_dict"][node_id]
                                    logger.error(f"🗑️ Removed node {node_id} from metadata_dict")
                        
                        # Write back to file
                        with open(vector_store_path, 'w') as f:
                            json.dump(vector_store_data, f)
                        logger.error(f"💾 EMBEDDING CLEANUP: removed {vector_nodes_removed_from_file} nodes from vector_store.json")
                    
                    await asyncio.sleep(0.1)  # Small delay to ensure filesystem sync
                    
                    # Now reload the index from the modified files
                    logger.error(f"🔄 Reloading index from modified storage files...")
                    
                    # Force reload the docstore from the modified file
                    from llama_index.core import StorageContext, load_index_from_storage
                    
                    try:
                        storage_context = StorageContext.from_defaults(persist_dir="storage")
                        new_index = load_index_from_storage(storage_context)
                        
                        # Replace the current index with the reloaded one
                        app.state.index = new_index
                        logger.error(f"🔄 Successfully reloaded index from storage files")
                        
                    except Exception as reload_error:
                        logger.error(f"❌ Failed to reload index: {reload_error}")
                        # Continue with the current index
                    
                except Exception as e:
                    logger.error(f"❌ Failed direct storage modification: {e}")
                    manual_cleanup_success = False
                
                # Final verification: Check if cleanup actually worked
                final_verification_nodes = []
                if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                    for node_id, doc_info in app.state.index.docstore.docs.items():
                        if hasattr(doc_info, 'ref_doc_id') and doc_info.ref_doc_id == ref_doc_id:
                            final_verification_nodes.append(node_id)
                
                if final_verification_nodes:
                    logger.error(f"❌ MANUAL CLEANUP FAILED - {len(final_verification_nodes)} nodes still exist:")
                    for node_id in final_verification_nodes:
                        logger.error(f"   👻 Zombie node: {node_id}")
                    failed_to_delete.append(ref_doc_id)
                elif manual_cleanup_success and nodes_removed > 0:
                    logger.error(f"✅ Manual cleanup SUCCESS: removed {nodes_removed} nodes for {ref_doc_id}")
                    successfully_deleted.append(ref_doc_id)
                else:
                    logger.error(f"❌ Manual cleanup FAILED for {ref_doc_id}")
                    failed_to_delete.append(ref_doc_id)
                    
            except Exception as e:
                logger.error(f"💥 EXCEPTION deleting reference document {ref_doc_id}: {e}")
                logger.error(f"💥 Exception type: {type(e)}")
                import traceback
                logger.error(f"💥 Full traceback: {traceback.format_exc()}")
                failed_to_delete.append(ref_doc_id)
        
        # Count total documents after
        total_docs_after = len(app.state.index.docstore.docs) if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs") else 0
        logger.error(f"📊 Total documents AFTER deletion: {total_docs_after}")
        logger.error(f"📊 Documents removed: {total_docs_before - total_docs_after}")
        
        # Persist changes
        logger.error(f"💾 Persisting index changes...")
        await persist_index()
        logger.error(f"💾 Index persistence complete")
        
        # Determine success
        success_count = len(successfully_deleted)
        total_count = len(ref_docs_to_delete)
        success_rate = success_count / total_count if total_count > 0 else 1.0
        
        logger.error(f"📈 DELETION RESULTS:")
        logger.error(f"   Successfully deleted: {success_count}")
        logger.error(f"   Failed to delete: {len(failed_to_delete)}")
        logger.error(f"   Success rate: {success_rate:.2%}")
        
        if success_rate >= 1.0:
            logger.error(f"🎉 Successfully deleted ALL {success_count} reference documents for SharePoint document {sharepoint_id}")
            return True
        elif success_rate >= 0.8:
            logger.error(f"⚠️  Partially deleted {success_count}/{total_count} reference documents for SharePoint document {sharepoint_id}")
            return True  # Consider 80%+ success as acceptable
        else:
            logger.error(f"❌ Failed to delete SharePoint document {sharepoint_id}: only {success_count}/{total_count} reference documents deleted")
            return False
            
    except Exception as e:
        logger.error(f"💥 CRITICAL ERROR in complete SharePoint document deletion for {sharepoint_id}: {e}", exc_info=True)
        return False


async def delete_document_with_verification(doc_id: str, max_retries: int = 3) -> bool:
    """
    Delete a document from the index with verification and retry logic.
    Uses manual deletion when LlamaIndex delete_ref_doc fails.
    
    Args:
        doc_id: Document ID to delete
        max_retries: Maximum number of deletion attempts
        
    Returns:
        bool: True if deletion was successful and verified
    """
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for document deletion")
        return False
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Attempting to delete document {doc_id} (attempt {attempt + 1}/{max_retries})")
            
            # Try LlamaIndex's delete_ref_doc first
            try:
                await asyncio.to_thread(
                    app.state.index.delete_ref_doc,
                    doc_id,
                    delete_from_docstore=True,
                )
                logger.info(f"LlamaIndex delete_ref_doc completed for {doc_id}")
            except Exception as e:
                logger.warning(f"LlamaIndex delete_ref_doc failed for {doc_id}: {e}")
            
            # Force refresh the storage context to ensure consistency
            await refresh_storage_context()
            
            # Verify the deletion worked
            if await verify_deletion(doc_id):
                logger.info(f"Document {doc_id} successfully deleted and verified")
                return True
            else:
                logger.warning(f"LlamaIndex deletion failed, attempting manual deletion for {doc_id}")
                
                # Manual deletion - directly remove from all storage components
                deletion_success = await manual_delete_document(doc_id)
                if deletion_success:
                    # Verify manual deletion worked
                    if await verify_deletion(doc_id):
                        logger.info(f"Document {doc_id} successfully manually deleted and verified")
                        return True
                    else:
                        logger.error(f"Manual deletion verification failed for {doc_id}")
                else:
                    logger.error(f"Manual deletion failed for {doc_id}")
                
        except Exception as e:
            logger.error(f"Error deleting document {doc_id} on attempt {attempt + 1}: {e}")
            
        # Wait before retry
        if attempt < max_retries - 1:
            await asyncio.sleep(1)
    
    logger.error(f"Failed to delete document {doc_id} after {max_retries} attempts")
    return False


async def manual_delete_document(doc_id: str) -> bool:
    """
    Manually delete a document from all storage components when LlamaIndex delete_ref_doc fails.
    This directly removes the document from docstore, vector store, and index structure.
    """
    try:
        logger.info(f"Starting manual deletion for document {doc_id}")
        deletion_success = True
        
        # 1. Remove from docstore
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            if doc_id in app.state.index.docstore.docs:
                del app.state.index.docstore.docs[doc_id]
                logger.info(f"✓ Manually removed {doc_id} from docstore")
            else:
                logger.info(f"Document {doc_id} not found in docstore")
        
        # 2. Remove from vector store
        if hasattr(app.state.index, "vector_store"):
            try:
                # Find all nodes belonging to this document from docstore
                nodes_to_remove = []
                if hasattr(app.state.index, 'docstore') and hasattr(app.state.index.docstore, 'docs'):
                    for node_id, node_info in app.state.index.docstore.docs.items():
                        if hasattr(node_info, 'ref_doc_id') and node_info.ref_doc_id == doc_id:
                            nodes_to_remove.append(node_id)
                
                # Try different vector store deletion approaches
                removed_count = 0
                vector_store = app.state.index.vector_store
                
                # For SimpleVectorStore with embedding_dict
                if hasattr(vector_store, '_data') and hasattr(vector_store._data, 'embedding_dict'):
                    for node_id in nodes_to_remove:
                        if node_id in vector_store._data.embedding_dict:
                            del vector_store._data.embedding_dict[node_id]
                            removed_count += 1
                
                # Alternative: direct embedding_dict access
                elif hasattr(vector_store, 'embedding_dict'):
                    for node_id in nodes_to_remove:
                        if node_id in vector_store.embedding_dict:
                            del vector_store.embedding_dict[node_id]
                            removed_count += 1
                
                # Use LlamaIndex's delete method if available
                elif hasattr(vector_store, 'delete'):
                    for node_id in nodes_to_remove:
                        try:
                            vector_store.delete(node_id)
                            removed_count += 1
                        except:
                            pass
                
                if removed_count > 0:
                    logger.info(f"✓ Manually removed {removed_count} nodes from vector store for document {doc_id}")
                else:
                    logger.info(f"No vector store nodes found for document {doc_id}")
                        
            except Exception as e:
                logger.warning(f"Could not manually remove from vector store: {e}")
                deletion_success = False
        
        # 3. Remove from index structure 
        if hasattr(app.state.index, 'index_struct') and hasattr(app.state.index.index_struct, 'nodes_dict'):
            if doc_id in app.state.index.index_struct.nodes_dict:
                del app.state.index.index_struct.nodes_dict[doc_id]
                logger.info(f"✓ Manually removed {doc_id} from index structure")
            else:
                logger.info(f"Document {doc_id} not found in index structure")
        
        # 4. Force persistence after manual deletion
        await persist_index()
        logger.info(f"✓ Persisted changes after manual deletion of {doc_id}")
        
        return deletion_success
        
    except Exception as e:
        logger.error(f"Error in manual deletion of {doc_id}: {e}", exc_info=True)
        return False


async def refresh_storage_context():
    """
    Refresh the storage context to ensure all components are synchronized.
    This helps resolve issues where deletions don't properly propagate.
    """
    try:
        if not hasattr(app.state, "index") or not app.state.index:
            logger.warning("Index not available for storage context refresh")
            return
            
        logger.info("Refreshing storage context for better deletion synchronization...")
        
        # Force persistence to ensure all changes are written to disk
        await persist_index()
        
        # Small delay to ensure filesystem operations complete
        await asyncio.sleep(0.1)
        
        logger.info("Storage context refresh completed")
        
    except Exception as e:
        logger.error(f"Error refreshing storage context: {e}", exc_info=True)


@app.get("/api/debug/evolution")
async def debug_evolution_documents(request: Request, user=CurrentUser):
    """Debug endpoint to check for Evolution documents in the index."""
    try:
        evolution_docs = []
        all_docs_count = 0
        
        if hasattr(app.state, "index") and hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                all_docs_count += 1
                
                # Check if this is an Evolution document
                file_name = doc_info.metadata.get("file_name", "") if doc_info.metadata else ""
                if "EVOLUTION" in file_name.upper():
                    evolution_docs.append({
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "sharepoint_id": doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None,
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown",
                        "created": doc_info.metadata.get("created", "Unknown") if doc_info.metadata else "Unknown"
                    })
        
        return JSONResponse(content={
            "total_documents": all_docs_count,
            "evolution_documents": evolution_docs,
            "evolution_count": len(evolution_docs)
        })
        
    except Exception as e:
        logger.error(f"Error in debug endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on application shutdown."""
    if hasattr(app.state, "index"):
        try:
            logger.info("Persisting index to storage...")
            await persist_index()
            logger.info("Index persisted successfully")
        except Exception as e:
            logger.error(f"Error persisting index: {e}")

    # --- Optional: Shutdown scheduler and delete subscription ---
    if hasattr(app.state, "scheduler") and app.state.scheduler.running:
        logger.info("Shutting down webhook renewal scheduler...")
        app.state.scheduler.shutdown()

    if (
        hasattr(app.state, "sharepoint_subscription_id")
        and app.state.sharepoint_subscription_id
        and hasattr(app.state, "sharepoint_client")
    ):
        logger.info(
            f"Attempting to delete webhook subscription: {app.state.sharepoint_subscription_id}"
        )
        app_token = await get_application_token()
        if app_token:
            try:
                await app.state.sharepoint_client.delete_webhook_subscription(
                    app.state.sharepoint_subscription_id, app_token
                )
                logger.info("Successfully deleted webhook subscription.")
            except Exception as e:
                logger.error(f"Failed to delete webhook subscription on shutdown: {e}")
        else:
            logger.warning(
                "Could not get application token to delete webhook subscription on shutdown."
            )


# --- Basic Auth Endpoints and Helpers ---
async def get_microsoft_access_token(request: Request) -> str:
    """Get Microsoft Graph API access token from session using server-side cache."""
    session = request.session
    ms_user = session.get("ms_user")
    if not ms_user:
        # Not logged in: API gets 401, browser redirects
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=401, detail="Microsoft authentication required"
            )
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    # Acquire token silently
    try:
        # Pick the first account in the cache to use for silent token acquisition
        accounts = msal_app.get_accounts()
        account = accounts[0] if accounts else None
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_SCOPES, account),
            timeout=10,
        )
    except Exception as e:
        logger.error(f"Error acquiring token silently: {e}")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=502, detail="Could not reach Azure AD token endpoint"
            )
        # <<< Store target URL before redirecting >>>
        session["post_auth_redirect_url"] = str(request.url)
        logger.info(
            f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
        )
        # <<< End store >>>
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    access_token = result.get("access_token") if isinstance(result, dict) else None
    if not access_token:
        # Silent acquisition failed: redirect to login
        logger.warning("Silent token acquisition failed, redirecting to login")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=401, detail="Microsoft authentication required"
            )
        # <<< Store target URL before redirecting >>>
        session["post_auth_redirect_url"] = str(request.url)
        logger.info(
            f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
        )
        # <<< End store >>>
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    return access_token


MsTokenDep = Depends(get_microsoft_access_token)


# --- Basic Auth Endpoints ---
@app.post("/login")
async def login(
    request: Request, credentials: HTTPBasicCredentials = Depends(security)
):
    # Verify credentials against settings (environment variables)
    correct_username = settings.USERNAME
    correct_password = settings.PASSWORD.get_secret_value()

    # Simple credential validation
    if not (
        credentials.username == correct_username
        and credentials.password == correct_password
    ):
        logger.warning(f"Failed login attempt for user: {credentials.username}")
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    # --- Clear previous session data before setting new ---
    session = request.session
    session.clear()
    # --------------------------------------------------------

    # Set user in session (only Basic Auth info)
    session["basic_user"] = {
        "username": credentials.username,
        "authenticated": True,
    }
    # Optionally set the general 'authenticated' flag if needed by other parts
    session["authenticated"] = True
    session["auth_method"] = "basic"

    logger.info(f"User '{credentials.username}' successfully logged in via Basic Auth")

    # Return success
    return {"authenticated": True, "username": credentials.username}


# New login endpoint
@app.get("/login")
async def login_page():
    """Display login page with Microsoft login button."""
    return HTMLResponse(
        """
        <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
            <img src="static/ddb-logo-login.png" alt="DDB Logo" style="width: 120px; margin: 0 auto 1.5rem; display: block;" onerror="this.onerror=null; this.src='static/ddb-logo-80x80.png';">
            <h1 style="font-size: 1.5rem; margin-bottom: 2rem; color: #ffd100;">DDBrain</h1>
            <a href="/login/microsoft" style="display: block; background-color: #0078d4; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                Sign in with Microsoft
            </a>
        </div>
        """
    )


@app.post("/logout")
async def logout(request: Request):
    # Clear the session
    session = request.session
    session.clear()
    logger.info("User logged out")
    return {"message": "Successfully logged out"}


@app.get("/user")
async def get_user_status(request: Request):
    # Get user from session or return unauthenticated
    session = request.session
    # <<< Log session content for debugging >>>
    logger.info(f"[/user] Session content: {dict(session)}")
    user = _extract_user(session)
    if user:
        logger.info(f"[/user] Found user in session: {user}")
        user["is_admin"] = user_is_admin(session)
        return user
    logger.info("[/user] No user found in session.")
    return {"authenticated": False, "is_admin": False}


@app.get("/login/microsoft", name="login_microsoft")
async def login_microsoft(request: Request, login_hint: Optional[str] = Query(None)):
    """Initiate Microsoft OAuth flow, optionally using a login hint."""
    try:
        if not msal_app:
            logger.error("Microsoft authentication is not configured")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Not Configured",
                    "error_message": "Microsoft authentication is not properly configured.",
                    "error_details": "Please check your environment variables: MS_CLIENT_ID, MS_CLIENT_SECRET, and MS_TENANT_ID",
                },
                status_code=500,
            )

        # Generate state for CSRF protection
        session = request.session
        state = str(uuid.uuid4())
        session["ms_auth_state"] = state
        logger.info(f"Generated MS auth state: {state}")
        if login_hint:
            logger.info(f"Received login_hint: {login_hint}")
        else:
            logger.info("No login_hint received.")

        # Generate the authorization URL, passing the hint if present
        auth_uri = msal_app.get_authorization_request_url(
            MSAL_SCOPES,
            state=state,
            redirect_uri=EXPECTED_REDIRECT,  # Use the correctly constructed URI
            login_hint=login_hint,  # <<< Pass the hint here
        )

        # Log the generated URL before redirecting
        logger.info(f"Generated Microsoft auth URL for redirect: {auth_uri}")

        # Return the redirect response
        # Use 303 See Other for POST-like behavior after GET
        return RedirectResponse(auth_uri, status_code=303)

    except Exception as e:
        logger.error(f"Error during Microsoft login initiation: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Microsoft Login Error",
                "error_message": "Failed to initiate Microsoft login.",
                "error_details": f"Error details: {str(e)}",
            },
            status_code=500,
        )


@app.get("/auth/callback")
async def auth_callback(
    request: Request,
    code: str = None,
    state: str = None,
    error: str = None,
    error_description: str = None,
):
    """Handle the Microsoft OAuth callback."""
    try:
        session = request.session
        logger.info(
            f"[/auth/callback] Starting. Session before processing: {dict(session)}"
        )

        # Handle potential errors from Microsoft
        if error:
            logger.error(f"Microsoft OAuth error: {error} - {error_description}")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Error",
                    "error_message": f"Error: {error}",
                    "error_details": error_description or "No details provided",
                },
                status_code=400,
            )

        # Verify state parameter to prevent CSRF attacks
        if not state or session.get("ms_auth_state") != state:
            expected_state = session.get("ms_auth_state", "No state in session")
            logger.error(
                f"State mismatch. Expected: {expected_state}, Received: {state}"
            )
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "Invalid authentication state.",
                    "error_details": "The state parameter did not match. This could indicate a cross-site request forgery attempt.",
                },
                status_code=400,
            )

        # Clear state after verification
        session.pop("ms_auth_state", None)
        logger.info(
            f"[/auth/callback] State verified and popped. Session: {dict(session)}"
        )

        # Process the callback with authorization code
        if code:
            # Exchange code for token in a background thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    msal_app.acquire_token_by_authorization_code,
                    code,
                    MSAL_SCOPES,
                    EXPECTED_REDIRECT,
                ),
                timeout=60,
            )

            if "error" in result:
                logger.error(
                    f"Token acquisition error: {result.get('error')} - {result.get('error_description')}"
                )
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "error_title": "Token Acquisition Error",
                        "error_message": f"Error: {result.get('error')}",
                        "error_details": result.get(
                            "error_description", "No details provided"
                        ),
                    },
                    status_code=400,
                )

            # Successfully acquired token
            logger.info("Token acquired successfully")

            # Get token claims and user info
            id_token_claims = result.get("id_token_claims", {})
            username = id_token_claims.get("preferred_username", "unknown")
            email = id_token_claims.get("email", username)
            name = id_token_claims.get("name", "User")

            logger.info(f"Microsoft login successful for {email}")

            # === Store the intended redirect target BEFORE clearing ===
            redirect_target = session.get("post_auth_redirect_url")
            logger.info(f"Retrieved redirect target before clear: {redirect_target}")
            # === End store target ===

            # Clear any existing session data first (wipes redirect_target from session)
            session.clear()
            logger.info("Session cleared after successful Microsoft token acquisition.")

            # === Set session data based *only* on Microsoft login ===
            # # Remove conditional restoration based on was_basic_admin
            # if was_basic_admin:
            #     session["basic_user"] = {
            #         "username": settings.USERNAME,  # Use the actual admin username
            #         "authenticated": True,
            #     }
            #     logger.info("Restored Basic Auth admin user to session.")
            # else:
            #     # If not restoring admin, set basic_user based on MS identity
            #     # --- We don't need basic_user if logged in via MS ---
            #     # session["basic_user"] = {
            #     #     "username": email,  # Use MS email as username
            #     #     "authenticated": True,
            #     # }
            #     pass # No need to set basic_user here

            # Store Microsoft user info
            session["ms_user"] = {"email": email, "name": name}
            logger.info(
                f"Set ms_user in session: {{'email': '{email}', 'name': '{name}'}}"
            )

            # Set general flags
            session["authenticated"] = True
            session["auth_method"] = "microsoft"
            logger.info(
                "Set authenticated=True and auth_method='microsoft' in session."
            )

            # Store token cache if available
            access_token = result.get("access_token")
            expires_in = result.get("expires_in")  # Duration in seconds
            # Calculate expiry time (epoch seconds) for easier checking later
            expires_on = time.time() + expires_in if expires_in else None
            if access_token and expires_on:
                session["ms_token_cache"] = {
                    "access_token": access_token,
                    "expires_on": expires_on,
                }
            else:
                # Ensure we log if the token cache *couldn't* be set
                logger.warning(
                    "ms_token_cache was NOT set in session (missing token or expiry)."
                )
            # === End setting session data ===

            # Log session JUST BEFORE redirect to confirm state
            logger.info(
                f"[/auth/callback] Session state before creating redirect response: {dict(session)}"
            )

            # === Determine redirect URL ===
            # Use the local variable captured before session clear
            # redirect_url = session.pop("post_auth_redirect_url", None)
            if not redirect_target:
                logger.info("No redirect_target stored, defaulting to homepage.")
                redirect_url = "/?login_success=true"  # Default redirect
            else:
                logger.info(f"Using stored redirect_target: {redirect_target}")
                redirect_url = redirect_target  # Use the stored URL
                # Optionally add the login_success flag if redirecting elsewhere
                if "?" in redirect_url:
                    redirect_url += "&login_success=true"
                else:
                    redirect_url += "?login_success=true"
            # === End determine redirect URL ===

            # Create response AFTER setting session data
            response = RedirectResponse(url=redirect_url, status_code=303)

            # Return the response (session middleware will handle saving it)
            return response
        else:
            logger.error("No authorization code provided in callback")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "No authorization code provided.",
                    "error_details": "The OAuth callback did not contain an authorization code.",
                },
                status_code=400,
            )

    except Exception as e:
        logger.error(f"Error during OAuth callback: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Authentication Error",
                "error_message": "An unexpected error occurred during authentication.",
                "error_details": str(e),
            },
            status_code=500,
        )


@app.get("/sharepoint/sites", name="list_sharepoint_sites")
async def list_sharepoint_sites_view(request: Request):
    """Render the SharePoint sites view for the authenticated user."""
    # <<< Add early entry log >>>
    logger.info("Entering list_sharepoint_sites_view")
    # <<< End early entry log >>>
    try:
        # <<< Log session content on entry >>>
        logger.info(
            f"[/sharepoint/sites] Session content on entry: {dict(request.session)}"
        )
        # <<< End log >>>

        logger.info("Starting list_sharepoint_sites_view endpoint")
        logger.info(f"Session content at /sharepoint/sites: {dict(request.session)}")

        # First verify SharePoint client is initialized
        if not hasattr(app.state, "sharepoint_client"):
            logger.error("SharePoint client is not initialized.")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "SharePoint Not Configured",
                    "error_message": "SharePoint integration is not configured or failed to initialize.",
                    "error_details": "Check your server configuration and environment variables.",
                },
                status_code=500,
            )

        # Check for authentication
        session = request.session
        is_authenticated = False
        if "basic_user" in session and session["basic_user"].get("authenticated"):
            is_authenticated = True
        elif "ms_user" in session:
            is_authenticated = True

        if not is_authenticated:
            logger.warning(
                f"No authentication found in session. Session keys: {list(session.keys())}"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        # Get the access token
        if "ms_token_cache" not in session:
            logger.warning(
                "No Microsoft token cache found in session, redirecting to login"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        cached_token = session.get("ms_token_cache", {})
        access_token = cached_token.get("access_token")

        if not access_token:
            logger.warning(
                "No valid access token found in token cache, redirecting to login"
            )
            # Clear any invalid session data
            if "ms_token_cache" in session:
                del session["ms_token_cache"]
            if "ms_user" in session:
                del session["ms_user"]
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        try:
            # List SharePoint sites
            logger.info("Calling SharePoint client to list sites")
            try:
                sites = await asyncio.wait_for(
                    app.state.sharepoint_client.list_sites(access_token), timeout=10
                )
            except asyncio.TimeoutError:
                logger.error("Timeout listing SharePoint sites")
                raise HTTPException(status_code=504, detail="Timeout listing sites")

            # Log the results for debugging
            site_count = len(sites) if sites else 0
            logger.info(f"Found {site_count} SharePoint sites")

            # Store the first site ID in the session (assuming it's the one we want)
            if sites and len(sites) > 0:
                session["current_site_id"] = sites[0].get("id", "")
                logger.info(f"Stored site ID in session: {session['current_site_id']}")

            return templates.TemplateResponse(
                "sharepoint_sites.html", {"request": request, "sites": sites}
            )
        except Exception as e:
            error_message = str(e).lower()
            # Check for any token-related errors
            if any(
                keyword in error_message
                for keyword in ["token", "401", "unauthorized", "expired"]
            ):
                logger.info("Token expired or invalid, redirecting to Microsoft login")
                # Clear the expired token
                if "ms_token_cache" in session:
                    del session["ms_token_cache"]
                if "ms_user" in session:
                    del session["ms_user"]
                redirect_url = request.url_for("login_microsoft")
                return RedirectResponse(url=str(redirect_url), status_code=302)
            raise  # Re-raise other exceptions

    except Exception as e:
        logger.error(f"Error in list_sharepoint_sites_view: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": str(e),
                "error_details": f"Exception type: {type(e).__name__}",
            },
            status_code=500,
        )


@app.get("/sharepoint/drives/{site_id}", name="list_sharepoint_drives")
async def list_sharepoint_drives(
    request: Request, site_id: str, user=CurrentUser, ms_token: str = MsTokenDep
):
    """List document libraries (drives) within a SharePoint site."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(f"Listing drives for site ID: {site_id}")
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # --- MODIFICATION START ---
        # Define the target site ID components and repository path
        # Site ID format: {hostname},{siteCollectionId},{siteId}
        target_hostname = "ddbgroupcomph-my.sharepoint.com"
        target_site_collection_id = "c05ba4f2-c263-4c53-a6be-e3e094c754d4"
        target_site_id_component = "40d66325-d5a1-4aa6-b0f4-0caa6705ed2d"
        target_full_site_id = (
            f"{target_hostname},{target_site_collection_id},{target_site_id_component}"
        )
        # --- Use drive-relative path (configured folder is at root level) ---
        target_repo_path = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
        # --- End path change ---

        # --- URL-decode the incoming site_id from the path ---
        site_id_decoded = unquote(site_id)
        logger.info(f"Decoded site_id for comparison: {site_id_decoded}")
        # --- End decode ---

        # Check if the requested site_id matches the target IT Storage site ID
        if site_id_decoded == target_full_site_id:
            logger.info(
                f"Detected target IT Storage site ID: {site_id_decoded}. Attempting to get its primary drive and redirect."
            )
            try:
                # Get drives specifically for the target site
                # Use the original (potentially encoded) site_id for the API call
                drives = await asyncio.wait_for(
                    app.state.sharepoint_client.list_drives(
                        site_id=site_id, token=ms_token
                    ),
                    timeout=10,
                )

                if drives:
                    # Assume the first drive is the primary one for a personal site
                    target_drive = drives[0]
                    target_drive_id = target_drive.get("id")
                    logger.info(
                        f"Found primary drive ID for IT Storage site: {target_drive_id}"
                    )

                    if target_drive_id:
                        try:
                            # Construct URL manually to avoid issues with url_for and query params
                            base_url_path = request.url_for(
                                "list_sharepoint_files", drive_id=target_drive_id
                            )
                            # URL-encode the folder path for the query string
                            encoded_folder_path = quote(target_repo_path, safe="")
                            redirect_url = (
                                f"{base_url_path}?folder_path={encoded_folder_path}"
                            )

                            logger.info(
                                f"Redirecting to IT Storage DDB Group Repository (manual URL): {redirect_url}"
                            )
                            return RedirectResponse(
                                url=str(redirect_url), status_code=302
                            )
                        except NoMatchFound:  # This except block should now work
                            logger.error(
                                f"Could not generate BASE URL for list_sharepoint_files (drive_id: {target_drive_id}). Route misconfigured?",
                                exc_info=True,
                            )
                            # Fall through to standard behavior if base URL generation fails
                        except Exception as url_err:  # Catch other potential errors during manual URL construction
                            logger.error(
                                f"Error constructing redirect URL: {url_err}",
                                exc_info=True,
                            )
                            # Fall through
                    else:
                        logger.warning(
                            "Could not extract drive ID from the drives list for IT Storage site."
                        )
                else:
                    logger.warning(
                        f"No drives found for the target IT Storage site ID: {site_id}"
                    )

            except asyncio.TimeoutError:
                logger.error(
                    f"Timeout listing drives for target IT Storage site {site_id}"
                )
                # Fall through to standard error handling
            except Exception as e:
                logger.error(
                    f"Error processing target IT Storage site {site_id}: {e}",
                    exc_info=True,
                )
                # Fall through to standard error handling

            # If redirect failed for any reason, fall through to rendering the drive list page for IT Storage
            logger.warning(
                f"Redirect failed for IT Storage site {site_id}. Falling back to rendering drive list."
            )
            # Note: 'drives' might be populated here from the attempt above
            if (
                "drives" not in locals()
            ):  # Ensure drives is defined if list_drives failed above
                drives = []
            return templates.TemplateResponse(
                "sharepoint_drives.html",
                {"request": request, "drives": drives, "site_id": site_id},
            )
        else:
            logger.info(
                f"Requested site_id {site_id} does not match target IT Storage site. Proceeding with standard drive listing."
            )
        # --- MODIFICATION END ---

        # Standard approach: List drives for non-target sites
        logger.info(f"Proceeding with standard drive listing for site_id: {site_id}")
        try:
            drives = await asyncio.wait_for(
                app.state.sharepoint_client.list_drives(
                    site_id=site_id, token=ms_token
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing drives for site {site_id}")
            raise HTTPException(status_code=504, detail="Timeout listing drives")
        logger.info(f"Found {len(drives)} drives for site {site_id}")

        return templates.TemplateResponse(
            "sharepoint_drives.html",
            {"request": request, "drives": drives, "site_id": site_id},
        )
    except Exception as e:
        logger.error(f"Error listing drives for site {site_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues (e.g., Graph API errors)
        error_detail = f"Failed to list document libraries: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list document libraries: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


@app.get("/sharepoint/files/{drive_id}", name="list_sharepoint_files")
async def list_sharepoint_files(
    request: Request,
    drive_id: str,
    folder_path: str = "",
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """List files and folders in a SharePoint drive or folder."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(
            f"Listing files for drive ID: {drive_id}, folder path: {folder_path}"
        )
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # Get the files from SharePoint
        try:
            files = await asyncio.wait_for(
                app.state.sharepoint_client.list_files(
                    drive_id=drive_id, token=ms_token, folder_path=folder_path
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing files for drive {drive_id}")
            raise HTTPException(status_code=504, detail="Timeout listing files")
        logger.info(f"Found {len(files)} files/folders")

        # Get the site ID from the session or a default value
        site_id = request.session.get("current_site_id", "")

        return templates.TemplateResponse(
            "sharepoint_files.html",
            {
                "request": request,
                "files": files,
                "drive_id": drive_id,
                "current_path": folder_path,
                "site_id": site_id,
            },
        )
    except Exception as e:
        logger.error(f"Error listing files for drive {drive_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues
        error_detail = f"Failed to list files: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list files: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


async def add_document_to_index(file_path: str, metadata: dict):
    """Loads a document from file_path, adds metadata, and inserts into the index."""
    try:
        file_name_for_log = metadata.get("file_name", Path(file_path).name)
        logger.info(
            f"Attempting to index document: {file_path} (File: {file_name_for_log})"
        )

        if not hasattr(app.state, "index") or not app.state.index:
            logger.error(f"Index not available, cannot add document: {file_path}")
            return

        # Check for existing document with same SharePoint ID
        sharepoint_id = metadata.get("sharepoint_id")
        if sharepoint_id and hasattr(app.state.index, "docstore"):
            # Find and remove any existing documents with the same SharePoint ID
            docs_to_delete = []
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                if (
                    doc_info.metadata
                    and doc_info.metadata.get("sharepoint_id") == sharepoint_id
                ):
                    docs_to_delete.append(doc_id)

            # Delete any found duplicates
            for doc_id in docs_to_delete:
                # Use improved deletion with verification for duplicate removal
                deletion_success = await delete_document_with_verification(doc_id)
                if deletion_success:
                    logger.info(f"Successfully removed duplicate document with SharePoint ID {sharepoint_id}")
                else:
                    logger.error(f"Failed to remove duplicate document with SharePoint ID {sharepoint_id}")

        # Use SimpleDirectoryReader to load the document
        reader = SimpleDirectoryReader(input_files=[file_path])
        documents = await asyncio.to_thread(reader.load_data)

        # Fix LlamaIndex default exclusions to allow file_name in embeddings
        for doc in documents:
            doc.excluded_embed_metadata_keys = []
            doc.excluded_llm_metadata_keys = []
            logger.info(f"Fixed metadata exclusions for document: {doc.metadata.get('file_name', 'Unknown')} - excluded_embed_keys: {doc.excluded_embed_metadata_keys}")

        if not documents:
            logger.warning(f"No content extracted from document: {file_path}")
            return

        nodes_to_insert = []
        for doc in documents:
            # Combine existing doc metadata with SharePoint metadata
            combined_metadata = {**doc.metadata, **metadata}

            # Ensure required metadata fields
            combined_metadata.setdefault(
                "file_name", metadata.get("file_name", Path(file_path).name)
            )
            combined_metadata.setdefault("web_url", metadata.get("web_url", ""))

            # Add enhanced sync tracking metadata
            combined_metadata.setdefault("indexed_datetime", datetime.now().isoformat())
            combined_metadata.setdefault("sync_version", "2.0")  # Version for tracking metadata format

            # Add sync path for tracking how document was added
            if not combined_metadata.get("sync_path"):
                if combined_metadata.get("sharepoint_id"):
                    combined_metadata["sync_path"] = "sharepoint_sync"
                else:
                    combined_metadata["sync_path"] = "manual_upload"

            # Add document hash for change detection (if not already present)
            if not combined_metadata.get("content_hash"):
                import hashlib
                content_hash = hashlib.md5(doc.get_content().encode()).hexdigest()
                combined_metadata["content_hash"] = content_hash

            doc.metadata = combined_metadata

            # Create TextNode for insertion
            node = TextNode(
                text=doc.get_content(metadata_mode=MetadataMode.ALL),
                metadata=doc.metadata,
                excluded_embed_metadata_keys=[],  # Include ALL metadata in embeddings, including file_name
                excluded_llm_metadata_keys=[],    # Include ALL metadata for LLM context as well
            )
            nodes_to_insert.append(node)

        if nodes_to_insert:
            # Insert nodes into the index
            await asyncio.to_thread(app.state.index.insert_nodes, nodes_to_insert)
            # Persist changes
            await persist_index()
            logger.info(
                f"Successfully added/updated {len(nodes_to_insert)} nodes in index from: {file_name_for_log}"
            )
        else:
            logger.info(f"No nodes generated for indexing from: {file_name_for_log}")

    except Exception as e:
        logger.error(
            f"Failed to index document {metadata.get('file_name', file_path)}: {e}",
            exc_info=True,
        )


async def import_sharepoint_item(request: Request, drive_id: str, item_id: str) -> dict:
    """Import a file or folder from SharePoint with enhanced batch processing and error handling."""
    temp_dir = None
    try:
        temp_dir = Path(settings.TEMP_DIR)
        temp_dir.mkdir(parents=True, exist_ok=True)
        access_token = await get_microsoft_access_token(request)
        item = await app.state.sharepoint_client.get_drive_item(
            drive_id, item_id, token=access_token
        )
        if not item:
            raise ValueError("Item not found")
        progress = {
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "skipped_files": [],
            "errors": [],
        }

        async def process_single_file(file_item, parent_path=""):
            file_path_for_error = file_item.get("name", "Unknown File")
            try:
                # --- Check if document already exists in index ---
                if (
                    hasattr(app.state, "index")
                    and app.state.index
                    and hasattr(app.state.index, "docstore")
                ):
                    existing_doc_id = None
                    target_sharepoint_id = file_item.get("id")
                    if target_sharepoint_id:
                        # Efficiently check if sharepoint_id exists in metadata
                        for doc_id, doc_info in app.state.index.docstore.docs.items():
                            if (
                                doc_info.metadata
                                and doc_info.metadata.get("sharepoint_id")
                                == target_sharepoint_id
                            ):
                                existing_doc_id = doc_id
                                break  # Found it

                    if existing_doc_id:
                        file_name_for_log = file_item.get("name", "Unknown File")
                        logger.info(
                            f"Skipping import for already indexed file: {file_name_for_log} (SharePoint ID: {target_sharepoint_id})"
                        )
                        progress["skipped_files"].append(
                            f"{file_item.get('name', 'Unknown File')} (already indexed)"
                        )
                        # Need to increment total_files here if we count skipped-as-existing towards total potential
                        # progress["total_files"] += 1 # Decide if this is desired
                        return  # Skip processing this file
                # --- End check ---

                file_name = file_item.get("name", "")
                file_path = parent_path + "/" + file_name if parent_path else file_name
                file_path_for_error = file_path
                file_size = file_item.get("size", 0)
                file_ext = Path(file_name).suffix.lower()

                # Check file size limitation
                if file_size > 100 * 1024 * 1024:
                    progress["skipped_files"].append(f"{file_path} (too large)")
                    return

                # Check supported file types
                if file_ext not in [
                    ".txt",
                    ".pdf",
                    ".doc",
                    ".docx",
                    ".rtf",
                    ".csv",
                    ".xlsx",
                    ".xls",
                    ".png",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".bmp",
                ]:
                    progress["skipped_files"].append(f"{file_path} (unsupported type)")
                    return

                # Download the file
                temp_file_path = temp_dir / file_name
                file_content = await app.state.sharepoint_client.download_file_content(
                    drive_id, file_item["id"], access_token
                )
                # Write content to temp file
                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Special handling for specific problematic file
                if "DDB_BrandGuidelines-2025.pdf" in str(temp_file_path):
                    logger.warning(f"Detected problematic PDF file: {temp_file_path}")
                    try:
                        # Create a stub entry for the problematic PDF
                        metadata = {
                            "sharepoint_id": file_item["id"],
                            "created_datetime": file_item.get("createdDateTime"),
                            "last_modified_datetime": file_item.get(
                                "lastModifiedDateTime"
                            ),
                            "created_by": file_item.get("createdBy", {})
                            .get("user", {})
                            .get("displayName"),
                            "web_url": file_item.get("webUrl"),
                            "parent_path": parent_path,
                            "is_stub": True,
                        }

                        # Create a TextNode directly with a stub message
                        stub_text = f"DDB Brand Guidelines (2025) - This document contains design and branding guidelines for DDB. Please access the original document on SharePoint for complete details."
                        node = TextNode(
                            text=stub_text,
                            metadata={**metadata, "file_name": file_name},
                        )
                        app.state.index.insert_nodes([node])
                        await persist_index()
                        progress["processed_files"] += 1
                        logger.info(f"Successfully created stub entry for {file_name}")
                        temp_file_path.unlink()
                        return
                    except Exception as e:
                        logger.error(f"Error creating stub for {file_path}: {str(e)}")
                        progress["failed_files"] += 1
                        progress["errors"].append(f"Error with {file_path}: {str(e)}")
                        # Ensure we return here to stop processing this file on error
                        return

                # Standard processing for other files
                metadata = {
                    "sharepoint_id": file_item["id"],
                    "created_datetime": file_item.get("createdDateTime"),
                    "last_modified_datetime": file_item.get("lastModifiedDateTime"),
                    "created_by": file_item.get("createdBy", {})
                    .get("user", {})
                    .get("displayName"),
                    "web_url": file_item.get("webUrl"),
                    "parent_path": parent_path,
                }
                await add_document_to_index(str(temp_file_path), metadata)
                progress["processed_files"] += 1
                temp_file_path.unlink()
            except Exception as e:
                error_msg = f"Error processing {file_path_for_error}: {str(e)}"
                logger.error(error_msg)
                progress["failed_files"] += 1
                progress["errors"].append(error_msg)

        async def process_folder(folder_item, parent_path=""):
            folder_name_for_error = folder_item.get("name", "Unknown Folder")
            try:
                folder_name = folder_item.get("name", "")
                folder_name_for_error = (
                    f"{parent_path}/{folder_name}" if parent_path else folder_name
                )
                new_parent = (
                    parent_path + "/" + folder_name if parent_path else folder_name
                )
                logger.info(f"Processing folder: {new_parent}")
                async for (
                    child
                ) in app.state.sharepoint_client.list_folder_contents_recursive(
                    drive_id, folder_item["id"], token=access_token
                ):
                    logger.debug(
                        f"Processing child item: Name={child.get('name')}, Keys={list(child.keys())}, IsFolder={child.get('folder')}, IsFile={child.get('file')}"
                    )
                    if "folder" in child:
                        await process_folder(child, new_parent)
                    else:
                        progress["total_files"] += 1
                        await process_single_file(child, new_parent)
            except Exception as e:
                error_msg = f"Error processing folder {folder_name_for_error}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                progress["errors"].append(error_msg)

        if "folder" in item:
            await process_folder(item)
        else:
            progress["total_files"] = 1
            await process_single_file(item)

        return {
            "status": "completed",
            "total_files": progress["total_files"],
            "processed_files": progress["processed_files"],
            "failed_files": progress["failed_files"],
            "skipped_files": progress["skipped_files"],
            "errors": progress["errors"],
        }

    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        return {"status": "failed", "error": str(e)}
    finally:
        if temp_dir and temp_dir.exists():
            shutil.rmtree(temp_dir)


@app.get(
    "/sharepoint/import/{drive_id}/{item_id}/{item_name}",
    name="import_sharepoint_item_route",
)
async def import_sharepoint_item_route(
    request: Request,
    drive_id: str,
    item_id: str,
    item_name: str,
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """
    Import a file or folder from SharePoint and render appropriate response.
    Requires Admin privileges.
    """
    # --- Admin Check ---
    session = request.session
    is_admin = False
    admin_emails_set = {
        email.strip().lower()
        for email in settings.ADMIN_EMAILS.split(",")
        if email.strip()
    }

    # Check 1: Basic Auth Admin User
    if (
        "basic_user" in session
        and session["basic_user"].get("authenticated")
        and session["basic_user"].get("username") == settings.USERNAME
    ):
        is_admin = True
        user_identifier = settings.USERNAME
        logger.debug(f"Admin access granted via Basic Auth user: {user_identifier}")

    # Check 2: Microsoft Logged-in User in Admin Emails List
    elif (
        "ms_user" in session
        and session["ms_user"].get("email")
        and session["ms_user"].get("email").lower() in admin_emails_set
    ):
        is_admin = True
        user_identifier = session["ms_user"]["email"]
        logger.debug(f"Admin access granted via Microsoft email: {user_identifier}")

    # If not admin by either method, raise Forbidden error
    if not is_admin:
        # Try to get identifier for logging purposes
        basic_user_info = session.get("basic_user", {})
        ms_user_info = session.get("ms_user", {})
        user_identifier = (
            basic_user_info.get("username")
            or ms_user_info.get("email")
            or "Unknown User"
        )

        logger.warning(
            f"Unauthorized attempt to access manual import by user: {user_identifier}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Manual SharePoint import is restricted to administrators.",
        )
    # --- End Admin Check ---

    try:
        # Log which admin user is performing the action
        logger.info(
            f"Admin user ({user_identifier}) initiated import for item: {item_name} (ID: {item_id}) from drive: {drive_id}"
        )
        # Call the import function, passing the request object
        result = await import_sharepoint_item(request, drive_id, item_id)

        if result["status"] == "completed":
            # Build success message
            message = f"Successfully processed {result['processed_files']} of {result['total_files']} files from '{item_name}'."
            details = []

            if result["skipped_files"]:
                details.append(f"Skipped files: {', '.join(result['skipped_files'])}")

            if result["failed_files"] > 0:
                details.append(f"Failed to process {result['failed_files']} files:")
                details.extend([f"  - {error}" for error in result["errors"]])

            return templates.TemplateResponse(
                "success.html",
                {
                    "request": request,
                    "message": message,
                    "details": "\n".join(details) if details else None,
                },
            )
        else:
            # Handle failed import
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Import Failed",
                    "error_message": f"Failed to import '{item_name}'",
                    "error_details": result.get("error", "Unknown error occurred"),
                },
                status_code=500,
            )

    except Exception as e:
        logger.error(f"Error in import route: {str(e)}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Import Error",
                "error_message": f"An unexpected error occurred while importing '{item_name}'",
                "error_details": str(e),
            },
            status_code=500,
        )




# --- RAG Query Endpoints ---
@app.get("/query")
async def query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=CurrentUser,
):
    """Query the RAG system with a natural language question."""
    # <<< Log session at query start >>>
    logger.debug(f"[/query] Session at start: {dict(request.session)}")
    # <<< End log >>>
    try:
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Failed to load vector index",
                            "details": str(e),
                        },
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(
                    f"Error creating query engine with current model: {model_error}"
                )

                # Try with a more reliable fallback model using isolated instances
                try:
                    logger.info(
                        "Attempting to create query engine with fallback model..."
                    )
                    # Create isolated fallback model instances
                    fallback_model_name = "gpt-3.5-turbo"
                    validate_model_name(fallback_model_name)
                    logger.info(f"Using validated fallback model: {fallback_model_name}")
                    
                    fallback_llm = LlamaOpenAI(
                        model=fallback_model_name,  # Fallback to a more widely available model
                        temperature=settings.LLM_TEMPERATURE,
                        max_tokens=settings.LLM_MAX_TOKENS,
                    )
                    fallback_embed_model = OpenAIEmbedding(model="text-embedding-3-small")

                    # Save original global settings
                    original_llm = getattr(LlamaSettings, 'llm', None)
                    original_embed_model = getattr(LlamaSettings, 'embed_model', None)
                    
                    try:
                        # Temporarily set fallback models for query engine creation
                        LlamaSettings.llm = fallback_llm
                        LlamaSettings.embed_model = fallback_embed_model

                        # Try again with fallback model
                        app.state.query_engine = get_query_engine(
                            app.state.index, app.state.reranker
                        )
                        logger.info("Successfully created query engine with fallback model")
                    finally:
                        # Restore original global settings to prevent contamination
                        if original_llm is not None:
                            LlamaSettings.llm = original_llm
                        elif hasattr(LlamaSettings, 'llm'):
                            delattr(LlamaSettings, 'llm')
                        
                        if original_embed_model is not None:
                            LlamaSettings.embed_model = original_embed_model
                        elif hasattr(LlamaSettings, 'embed_model'):
                            delattr(LlamaSettings, 'embed_model')
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Query engine initialization failed",
                            "details": f"Original error: {model_error}. Fallback error: {fallback_error}",
                        },
                    )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error(
                "Query engine still not available after initialization attempts"
            )
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to initialize query engine",
                    "details": "Please contact the administrator",
                },
            )

        # Process query and get result
        logger.info(f"Processing query: {query}")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a comprehensive, well-structured answer to the following question. Your response should be thorough, informative, and easy to follow.

CRITICAL INSTRUCTION:
- ONLY answer based on the information provided in the context documents
- If the context doesn't contain relevant information about the question, clearly state: "I don't have information about [topic] in the available documents"
- DO NOT make up or invent information that isn't in the provided context
- DO NOT try to answer questions about topics not covered in the documents

FORMATTING REQUIREMENTS:
- Use markdown formatting with **bold text** for key concepts and important points
- Create bullet points (-) or numbered lists for step-by-step instructions or multiple items
- Use clear paragraph breaks for better readability
- Include relevant subheadings (##) to organize complex topics

CONTENT REQUIREMENTS:
- Provide detailed explanations with context and background information
- Include specific examples, use cases, or scenarios when relevant
- Break down complex concepts into understandable parts
- Explain the reasoning behind recommendations or procedures
- Mention any important considerations, limitations, or exceptions
- Reference relevant company policies, procedures, or guidelines when applicable
- If discussing processes, include the complete workflow from start to finish

DEPTH REQUIREMENTS:
- Go beyond basic answers - provide comprehensive coverage of the topic
- Anticipate follow-up questions and address them proactively
- Include practical implementation details
- Explain both the "what" and the "why" behind information
- Provide actionable insights that users can immediately apply

Question: {query}"""

        # Process query and get result - run sync query method in thread
        result = await asyncio.to_thread(app.state.query_engine.query, enhanced_query)
        logger.info(f"Query result type: {type(result)}")  # Log type for debugging

        # --- Log raw result structure ---
        logger.debug(f"Raw query result object: {result}")
        logger.debug(f"Raw query result source nodes: {result.source_nodes}")
        logger.debug(f"Raw query result metadata: {result.metadata}")
        # --- End log raw result structure ---

        # Handle streaming response object from LlamaIndex
        source_nodes = result.source_nodes if hasattr(result, 'source_nodes') else []
        
        # Extract response content from streaming response
        if hasattr(result, 'response_gen'):
            # Collect all chunks from the streaming response
            response_chunks = []
            try:
                for chunk in result.response_gen:
                    response_chunks.append(str(chunk))
                response_content = ''.join(response_chunks)
            except Exception as e:
                logger.warning(f"Error collecting streaming chunks: {e}")
                response_content = result.response if hasattr(result, 'response') else "Error processing response"
        elif hasattr(result, 'response'):
            response_content = result.response
        else:
            response_content = str(result)
        
        # Apply relevance filtering and source limiting
        MINIMUM_RELEVANCE_THRESHOLD = 0.3  # 30% relevance threshold
        MAX_SOURCES_DEFAULT = 5  # Limit to top 5 sources by default
        
        # Track source counts for metadata
        total_sources = len(source_nodes) if source_nodes else 0
        relevant_sources_count = 0
        displayed_sources_count = 0
        
        # Filter out low-scoring sources and limit to top sources
        if source_nodes:
            # Filter by relevance score
            filtered_source_nodes = [
                node for node in source_nodes 
                if getattr(node, "score", 0) >= MINIMUM_RELEVANCE_THRESHOLD
            ]
            relevant_sources_count = len(filtered_source_nodes)
            # Limit to maximum number of sources
            source_nodes = filtered_source_nodes[:MAX_SOURCES_DEFAULT]
            displayed_sources_count = len(source_nodes)
            logger.info(f"Sources: {total_sources} total, {relevant_sources_count} relevant, showing {displayed_sources_count}")
        
        # Prepare follow-up questions (Example logic, adjust as needed)
        follow_up_questions = []
        # <<< Restore manual follow-up question generation >>>
        try:
            # Check if LLM is available on app state
            if hasattr(app.state, "llm") and app.state.llm:
                # Check if response indicates no relevant documents
                response_lower = response_content.lower()
                has_no_info_indicators = any(phrase in response_lower for phrase in [
                    "i don't have information",
                    "no information available",
                    "i don't have any relevant documents",
                    "no relevant documents",
                    "i cannot find",
                    "not available in the documents",
                    "no documents contain"
                ])
                
                # If response indicates no relevant information, clear source nodes for consistency
                if has_no_info_indicators:
                    source_nodes = []
                    relevant_sources_count = 0
                    displayed_sources_count = 0
                    logger.info(f"Response indicates no relevant information - clearing source nodes")
                
                # Only generate follow-up questions if we have relevant sources AND the response doesn't indicate lack of information
                if source_nodes and not has_no_info_indicators:
                    # Get source context information
                    source_context = ""
                    source_info = []
                    for node in source_nodes[:3]:  # Use top 3 sources
                        metadata = node.node.metadata if hasattr(node, "node") else node.metadata
                        if metadata and 'file_name' in metadata:
                            source_info.append(metadata['file_name'])
                    
                    if source_info:
                        source_context = f"\n\nThis answer was generated from these company documents: {', '.join(source_info)}. Focus on workplace policies, procedures, and company-related topics."

                    prompt = f"""Based on the comprehensive answer provided about '{query}' and the source documents used, generate exactly 3 strategic follow-up questions that would help the user gain deeper insights. The questions should be thoughtful, actionable, and designed to explore different aspects of the topic.

QUESTION CRITERIA:
- Each question should explore a different dimension (practical application, deeper context, related procedures, compliance aspects, implementation details, etc.)
- Questions should be specific enough to generate comprehensive answers
- Focus on actionable insights that help with real-world workplace scenarios
- Consider both immediate next steps and broader strategic implications
- Questions should encourage deeper exploration of company policies, procedures, and best practices

SOURCE CONTEXT: {source_context}

ORIGINAL QUERY: {query}

COMPREHENSIVE ANSWER PROVIDED:
{response_content}

Generate exactly 3 follow-up questions that would be most valuable for someone seeking to fully understand and implement this information in their work. Output ONLY the questions, each on a new line, without numbering or formatting."""

                    # Use await directly as llm.acomplete should be async
                    fu_resp = await app.state.llm.acomplete(prompt)

                    # Parse response, removing potential numbering/bullets
                    questions_raw = fu_resp.text.strip().split("\n")
                    follow_up_questions = [
                        q.strip(" *-1234567890.) ")
                        for q in questions_raw
                        if q.strip()
                        and len(q.strip()) > 5  # Basic check for valid question
                    ][:3]  # Take at most 3
                    logger.info(f"Generated follow-up questions: {follow_up_questions}")
                else:
                    logger.info(f"Skipping follow-up question generation - no relevant sources ({len(source_nodes) if source_nodes else 0}) or response indicates lack of information")
            else:
                logger.warning(
                    "LLM not available on app state, cannot generate follow-up questions."
                )
        except Exception as fu_err:
            logger.warning(
                f"Follow-up question generation failed: {fu_err}", exc_info=True
            )
        # <<< End restore >>>

        logger.info(
            f"Query response content: {response_content[:200]}..."
        )  # Log snippet

        return JSONResponse(
            status_code=200,
            content={
                "status": "completed",
                "response": response_content,  # Send the actual response string
                "source_nodes": [
                    {
                        # Use node.node to access the underlying TextNode if it's NodeWithScore
                        "text": node.node.text if hasattr(node, "node") else node.text,
                        "snippet": (
                            node.node.text[:200]
                            + ("…" if len(node.node.text) > 200 else "")
                        )
                        if hasattr(node, "node")
                        else (node.text[:200] + ("…" if len(node.text) > 200 else "")),
                        "score": getattr(node, "score", None),
                        "metadata": node.node.metadata
                        if hasattr(node, "node")
                        else node.metadata,
                    }
                    for node in source_nodes
                ],
                "follow_up_questions": follow_up_questions,
                "source_metadata": {
                    "total_sources": total_sources,
                    "relevant_sources": relevant_sources_count,
                    "displayed_sources": displayed_sources_count,
                    "has_more_sources": relevant_sources_count > displayed_sources_count
                },
            },
        )

    except Exception as e:
        logger.error(f"Error in query endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "An unexpected error occurred while processing the query",
                "details": str(e),
            },
        )


@app.get("/api/documents")
async def get_indexed_documents(request: Request, user=CurrentUser):
    """Returns metadata for documents currently in the vector index with sync status."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for /api/documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    docs_metadata = []
    try:
        # Access the document store associated with the index
        if hasattr(app.state.index, "docstore") and hasattr(
            app.state.index.docstore, "docs"
        ):
            # Iterate through documents in the docstore
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                # Ensure metadata exists and add doc_id for reference
                metadata = doc_info.metadata or {}
                metadata["doc_id"] = doc_id  # Add the internal doc_id
                
                # Add SharePoint availability status if document has SharePoint ID
                sharepoint_id = metadata.get("sharepoint_id")
                if sharepoint_id and settings.USE_SHAREPOINT:
                    metadata["has_sharepoint_id"] = True
                    # We could check availability here, but it would be too slow for many documents
                    # Instead, we'll mark documents that might need checking
                    metadata["sync_status"] = "unknown"  # Will be checked during sync
                else:
                    metadata["has_sharepoint_id"] = False
                    metadata["sync_status"] = "local_only"
                
                docs_metadata.append(metadata)
        else:
            logger.warning("Could not retrieve document metadata from index docstore.")

        # Add sync information to the response
        sync_info = {
            "auto_sync_enabled": settings.AUTO_SYNC_ENABLED,
            "sync_interval_minutes": settings.SYNC_INTERVAL_MINUTES,
            "last_sync_time": getattr(app.state, 'last_sync_time', None),
            "sharepoint_configured": settings.USE_SHAREPOINT and settings.SYNC_TARGET_DRIVE_ID is not None
        }
        
        # Convert datetime to string for JSON serialization
        if sync_info["last_sync_time"]:
            sync_info["last_sync_time"] = sync_info["last_sync_time"].isoformat()

        logger.info(f"Returning metadata for {len(docs_metadata)} indexed documents.")
        return JSONResponse(content={
            "documents": docs_metadata,
            "sync_info": sync_info,
            "total_documents": len(docs_metadata)
        })

    except Exception as e:
        logger.error(f"Error retrieving indexed documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve indexed documents metadata."
        )


@app.post("/api/documents/clear")
async def clear_indexed_documents(request: Request, user=CurrentUser):
    """Clears all documents from the vector index with detailed logging."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for clearing documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Log current index state before clearing
        doc_count = 0
        orphaned_count = 0
        sharepoint_count = 0

        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                doc_count += 1
                if doc_info.metadata and doc_info.metadata.get("sharepoint_id"):
                    sharepoint_count += 1
                else:
                    orphaned_count += 1

        logger.info(f"Clearing index with {doc_count} total documents: {sharepoint_count} with SharePoint ID, {orphaned_count} orphaned")

        # Create a new empty index
        app.state.index = await create_empty_index()
        await persist_index()

        logger.info("Successfully cleared all documents from index")
        return JSONResponse(content={
            "message": "All documents cleared successfully",
            "cleared_stats": {
                "total_documents": doc_count,
                "sharepoint_documents": sharepoint_count,
                "orphaned_documents": orphaned_count
            }
        })
    except Exception as e:
        logger.error(f"Error clearing documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to clear documents from index."
        )


@app.post("/api/documents/cleanup-orphaned")
async def cleanup_orphaned_documents(request: Request, user=CurrentUser):
    """Identifies and removes documents without SharePoint ID metadata."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for orphaned document cleanup")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        orphaned_docs = []
        sharepoint_docs = []

        # Identify orphaned documents
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                if doc_info.metadata and doc_info.metadata.get("sharepoint_id"):
                    sharepoint_docs.append({
                        "doc_id": doc_id,
                        "sharepoint_id": doc_info.metadata.get("sharepoint_id"),
                        "file_name": doc_info.metadata.get("file_name", "Unknown"),
                        "source": doc_info.metadata.get("source", "Unknown")
                    })
                else:
                    orphaned_docs.append({
                        "doc_id": doc_id,
                        "file_name": doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata",
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                    })

        logger.info(f"Found {len(orphaned_docs)} orphaned documents and {len(sharepoint_docs)} SharePoint documents")

        # Remove orphaned documents if SharePoint sync is enabled
        deleted_count = 0
        errors = []

        if settings.USE_SHAREPOINT and orphaned_docs:
            logger.info(f"Removing {len(orphaned_docs)} orphaned documents (SharePoint sync enabled)")

            for orphaned_doc in orphaned_docs:
                doc_id = orphaned_doc["doc_id"]
                # Use improved deletion with verification
                deletion_success = await delete_document_with_verification(doc_id)
                if deletion_success:
                    logger.info(f"Successfully deleted orphaned document: {orphaned_doc['file_name']} (doc_id: {doc_id})")
                    deleted_count += 1
                else:
                    error_msg = f"Failed to delete and verify orphaned document {orphaned_doc['file_name']} (doc_id: {doc_id})"
                    logger.error(error_msg)
                    errors.append(error_msg)

            # Persist changes if any deletions occurred
            if deleted_count > 0:
                logger.info("Persisting index changes after orphaned document cleanup...")
                await persist_index()
                logger.info("Index persistence complete.")

        return JSONResponse(content={
            "message": f"Orphaned document cleanup complete",
            "orphaned_documents_found": len(orphaned_docs),
            "sharepoint_documents_found": len(sharepoint_docs),
            "orphaned_documents_deleted": deleted_count,
            "sharepoint_sync_enabled": settings.USE_SHAREPOINT,
            "errors": errors,
            "orphaned_documents": orphaned_docs[:10],  # Return first 10 for inspection
            "sharepoint_documents_sample": sharepoint_docs[:5]  # Return first 5 for inspection
        })

    except Exception as e:
        logger.error(f"Error during orphaned document cleanup: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to cleanup orphaned documents."
        )


@app.get("/api/documents/{document_id}/view")
async def view_document(
    document_id: str, 
    request: Request, 
    user=CurrentUser, 
    ms_token: str = MsTokenDep
):
    """
    Serves a document by re-downloading it from SharePoint using the document metadata.
    This endpoint enables viewing documents without direct SharePoint authentication.
    Accepts either doc_id or sharepoint_id as the document_id parameter.
    """
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for document viewing")
        raise HTTPException(status_code=503, detail="Index is not ready.")
    
    try:
        # Find the document in the index - try both doc_id and sharepoint_id
        document_metadata = None
        found_doc_id = None
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            # First try to find by doc_id
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                if doc_id == document_id:
                    document_metadata = doc_info.metadata
                    found_doc_id = doc_id
                    break
            
            # If not found by doc_id, try to find by sharepoint_id
            if not document_metadata:
                for doc_id, doc_info in app.state.index.docstore.docs.items():
                    if doc_info.metadata and doc_info.metadata.get("sharepoint_id") == document_id:
                        document_metadata = doc_info.metadata
                        found_doc_id = doc_id
                        break
        
        if not document_metadata:
            logger.warning(f"Document not found in index: {document_id}")
            raise HTTPException(status_code=404, detail="Document not found.")
        
        # Get required metadata for SharePoint download
        sharepoint_id = document_metadata.get("sharepoint_id")
        drive_id = document_metadata.get("drive_id") or settings.SYNC_TARGET_DRIVE_ID
        file_name = document_metadata.get("file_name", "document")
        
        if not sharepoint_id:
            logger.error(f"No SharePoint ID found for document: {document_id}")
            raise HTTPException(
                status_code=400, 
                detail="Document does not have SharePoint information for viewing."
            )
        
        if not drive_id:
            logger.error(f"No drive ID available for document: {document_id}")
            raise HTTPException(
                status_code=500, 
                detail="SharePoint drive information not available."
            )
        
        # Download file from SharePoint
        try:
            logger.info(f"Downloading document for viewing: {file_name} (SharePoint ID: {sharepoint_id})")
            
            # Use the SharePoint client to download the file content
            file_content = await app.state.sharepoint_client.download_file_content(
                drive_id=drive_id,
                file_id=sharepoint_id,
                access_token=ms_token
            )
            
            if not file_content:
                raise HTTPException(
                    status_code=404, 
                    detail="Document content could not be retrieved from SharePoint."
                )
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(file_name)
            if not mime_type:
                # Default MIME types for common file extensions
                extension = Path(file_name).suffix.lower()
                mime_type_map = {
                    '.pdf': 'application/pdf',
                    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    '.doc': 'application/msword',
                    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    '.xls': 'application/vnd.ms-excel',
                    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    '.ppt': 'application/vnd.ms-powerpoint',
                    '.txt': 'text/plain',
                    '.md': 'text/markdown',
                    '.png': 'image/png',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.gif': 'image/gif',
                    '.bmp': 'image/bmp'
                }
                mime_type = mime_type_map.get(extension, 'application/octet-stream')
            
            # Set up response headers for proper document viewing
            headers = {
                'Content-Type': mime_type,
                'Content-Disposition': f'inline; filename="{file_name}"',
                'Content-Length': str(len(file_content)),
                'Cache-Control': 'private, max-age=300',  # Cache for 5 minutes
                'X-Content-Type-Options': 'nosniff'
            }
            
            # For PDF files, add additional headers to ensure proper browser display
            if mime_type == 'application/pdf':
                headers['X-Frame-Options'] = 'SAMEORIGIN'
            
            logger.info(f"Successfully serving document: {file_name} ({len(file_content)} bytes, {mime_type})")
            
            # Return the file content as a streaming response
            return Response(
                content=file_content,
                media_type=mime_type,
                headers=headers
            )
            
        except Exception as download_error:
            logger.error(f"Failed to download document from SharePoint: {download_error}")
            raise HTTPException(
                status_code=502, 
                detail=f"Failed to retrieve document from SharePoint: {str(download_error)}"
            )
    
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error serving document {document_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail="An unexpected error occurred while serving the document."
        )


@app.get("/api/sync/verify")
async def verify_sync_status(request: Request, user=CurrentUser, ms_token: str = MsTokenDep):
    """Verifies sync status by comparing SharePoint vs Index document counts and identifying discrepancies."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for sync verification")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Get SharePoint files
        known_working_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"

        sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
        sharepoint_files = await app.state.sharepoint_client.list_files(
            drive_id=known_working_drive_id,
            token=ms_token,
            folder_path=sync_folder
        )

        sharepoint_file_ids = set()
        sharepoint_file_names = {}

        for item in sharepoint_files:
            if "file" in item:
                file_id = item.get("id")
                file_name = item.get("name", "Unknown")
                sharepoint_file_ids.add(file_id)
                sharepoint_file_names[file_id] = file_name

        # Get Index documents
        indexed_docs = []
        indexed_sp_ids = set()
        orphaned_docs = []

        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"

                if sp_id:
                    indexed_sp_ids.add(sp_id)
                    indexed_docs.append({
                        "doc_id": doc_id,
                        "sharepoint_id": sp_id,
                        "file_name": file_name,
                        "in_sharepoint": sp_id in sharepoint_file_ids
                    })
                else:
                    orphaned_docs.append({
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                    })

        # Calculate discrepancies
        missing_from_index = sharepoint_file_ids - indexed_sp_ids
        missing_from_sharepoint = indexed_sp_ids - sharepoint_file_ids

        missing_from_index_details = [
            {"sharepoint_id": sp_id, "file_name": sharepoint_file_names.get(sp_id, "Unknown")}
            for sp_id in missing_from_index
        ]

        missing_from_sharepoint_details = [
            doc for doc in indexed_docs if doc["sharepoint_id"] in missing_from_sharepoint
        ]

        sync_health = "perfect" if not missing_from_index and not missing_from_sharepoint and not orphaned_docs else "issues_detected"

        return JSONResponse(content={
            "sync_status": sync_health,
            "sharepoint_file_count": len(sharepoint_file_ids),
            "indexed_document_count": len(indexed_docs),
            "orphaned_document_count": len(orphaned_docs),
            "missing_from_index_count": len(missing_from_index),
            "missing_from_sharepoint_count": len(missing_from_sharepoint),
            "missing_from_index": missing_from_index_details[:10],  # First 10
            "missing_from_sharepoint": missing_from_sharepoint_details[:10],  # First 10
            "orphaned_documents": orphaned_docs[:10],  # First 10
            "sync_recommendations": {
                "clear_and_resync": len(orphaned_docs) > 0,
                "manual_sync_needed": len(missing_from_index) > 0,
                "cleanup_needed": len(missing_from_sharepoint) > 0
            }
        })

    except Exception as e:
        logger.error(f"Error during sync verification: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to verify sync status."
        )


@app.get("/api/sync/background-status")
async def get_background_sync_status(request: Request, user=CurrentUser):
    """Get comprehensive status of the enhanced background sync system."""
    try:
        # Basic sync configuration
        sync_info = {
            "auto_sync_enabled": settings.AUTO_SYNC_ENABLED,
            "sync_interval_minutes": settings.SYNC_INTERVAL_MINUTES,
            "last_sync_time": getattr(app.state, 'last_sync_time', None),
            "sharepoint_configured": settings.USE_SHAREPOINT and settings.SYNC_TARGET_DRIVE_ID is not None,
            "background_sync_running": settings.AUTO_SYNC_ENABLED and settings.USE_SHAREPOINT and settings.SYNC_TARGET_DRIVE_ID
        }

        # Enhanced sync configuration
        enhanced_sync_config = {
            "auto_sync_enabled": settings.AUTO_SYNC_ENABLED,
            "auto_import_enabled": settings.AUTO_IMPORT_ENABLED,
            "auto_delete_enabled": settings.AUTO_DELETE_ENABLED,
            "max_files_per_sync": settings.MAX_FILES_PER_SYNC,
            "max_file_size_auto_import_mb": settings.MAX_FILE_SIZE_AUTO_IMPORT // (1024 * 1024),
            "auto_sync_require_manual_approval": settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
            "auto_sync_notification_email": settings.AUTO_SYNC_NOTIFICATION_EMAIL,
            "auto_sync_error_threshold": settings.AUTO_SYNC_ERROR_THRESHOLD,
            "sync_target_drive_id": settings.SYNC_TARGET_DRIVE_ID,
            "sync_target_folder_path": settings.SYNC_TARGET_FOLDER_PATH
        }

        # Get last sync statistics if available
        last_sync_stats = getattr(app.state, 'last_sync_stats', {})

        # Convert datetime to string for JSON serialization
        if sync_info["last_sync_time"]:
            sync_info["last_sync_time"] = sync_info["last_sync_time"].isoformat()

        # Get system resources
        resource_status = check_system_resources()

        # Get recent notifications summary
        recent_notifications = get_recent_notifications(days=1, unacknowledged_only=True)
        notification_summary = {
            "unacknowledged_count": len(recent_notifications),
            "recent_types": list(set([n.get("type", "unknown") for n in recent_notifications[-5:]])),
            "urgent_notifications": [n for n in recent_notifications if n.get("type") in ["sync_disabled", "resource_critical"]]
        }

        # Enhanced recommendations
        recommendations = {
            "manual_sync_needed": last_sync_stats.get("sync_needed", False),
            "new_files_available": last_sync_stats.get("new_files_detected", 0) > 0,
            "deletions_detected": last_sync_stats.get("deleted_files_detected", 0) > 0,
            "orphaned_cleanup_needed": last_sync_stats.get("orphaned_documents_detected", 0) > 0,
            "enable_auto_import": not settings.AUTO_IMPORT_ENABLED and last_sync_stats.get("new_files_detected", 0) > 0,
            "check_notifications": notification_summary["unacknowledged_count"] > 0,
            "resource_warning": resource_status.get("disk", {}).get("low_space_warning", False) or 
                               resource_status.get("memory", {}).get("high_usage_warning", False),
            "sync_errors_detected": last_sync_stats.get("consecutive_errors", 0) > 0,
            "sync_disabled_due_to_errors": last_sync_stats.get("sync_disabled", False)
        }

        # Performance metrics
        performance_metrics = {
            "files_processed_last_sync": last_sync_stats.get("files_processed", 0),
            "import_success_rate": 0 if last_sync_stats.get("new_files_detected", 0) == 0 else 
                                  (last_sync_stats.get("files_imported", 0) / last_sync_stats.get("new_files_detected", 1)) * 100,
            "last_sync_duration_seconds": last_sync_stats.get("last_sync_duration", 0),
            "consecutive_errors": last_sync_stats.get("consecutive_errors", 0),
            "max_files_limit_reached": last_sync_stats.get("max_files_limit_reached", False),
            "sync_mode": last_sync_stats.get("sync_mode", "detection")
        }

        # Health assessment
        health_status = "healthy"
        health_issues = []
        
        if last_sync_stats.get("sync_disabled", False):
            health_status = "critical"
            health_issues.append("Sync disabled due to errors")
        elif last_sync_stats.get("consecutive_errors", 0) > 0:
            health_status = "warning"
            health_issues.append(f"Consecutive errors: {last_sync_stats.get('consecutive_errors', 0)}")
        
        if resource_status.get("disk", {}).get("critical_space_warning", False):
            health_status = "critical"
            health_issues.append("Critical disk space")
        elif resource_status.get("disk", {}).get("low_space_warning", False):
            health_status = "warning" if health_status == "healthy" else health_status
            health_issues.append("Low disk space")
        
        if notification_summary["unacknowledged_count"] > 5:
            health_status = "warning" if health_status == "healthy" else health_status
            health_issues.append(f"{notification_summary['unacknowledged_count']} unacknowledged notifications")

        return JSONResponse(content={
            "sync_info": sync_info,
            "enhanced_sync_config": enhanced_sync_config,
            "last_sync_stats": last_sync_stats,
            "resource_status": resource_status,
            "notification_summary": notification_summary,
            "recommendations": recommendations,
            "performance_metrics": performance_metrics,
            "health_assessment": {
                "status": health_status,
                "issues": health_issues,
                "resource_monitoring_available": PSUTIL_AVAILABLE,
                "features_enabled": {
                    "auto_import": settings.AUTO_IMPORT_ENABLED,
                    "auto_delete": settings.AUTO_DELETE_ENABLED,
                    "notifications": settings.AUTO_SYNC_NOTIFICATION_EMAIL is not None,
                    "error_recovery": True,
                    "resource_monitoring": PSUTIL_AVAILABLE
                }
            }
        })

    except Exception as e:
        logger.error(f"Error getting enhanced background sync status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to get background sync status."
        )


@app.get("/api/sync/sidebar-notifications")
async def get_sidebar_notifications():
    """Get active sidebar notifications for real-time display."""
    try:
        # Get recent unacknowledged notifications for sidebar
        recent_notifications = get_recent_notifications(days=1, unacknowledged_only=True)
        
        # Filter and format for sidebar display
        sidebar_notifications = []
        for notification in recent_notifications:
            notification_type = notification.get('type', '')
            message = notification.get('message', '')
            details = notification.get('details', {})
            timestamp = notification.get('timestamp', '')
            
            # Create sidebar-friendly notification
            sidebar_item = {
                "id": f"{notification_type}_{timestamp}",
                "type": notification_type,
                "message": message,
                "timestamp": timestamp,
                "urgency": get_notification_urgency(notification_type),
                "icon": get_notification_icon(notification_type),
                "action_needed": requires_action(notification_type),
                "change_count": get_change_count(details),
                "quick_actions": get_quick_actions(notification_type, details)
            }
            sidebar_notifications.append(sidebar_item)
        
        # Add background sync status
        sync_status = "unknown"
        if hasattr(app.state, 'last_sync_stats'):
            sync_status = app.state.last_sync_stats.get('status', 'unknown')
        
        return JSONResponse(content={
            "notifications": sidebar_notifications,
            "total_count": len(sidebar_notifications),
            "has_urgent": any(n.get('urgency') == 'high' for n in sidebar_notifications),
            "sync_status": sync_status,
            "last_update": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting sidebar notifications: {e}", exc_info=True)
        return JSONResponse(content={
            "notifications": [],
            "total_count": 0,
            "has_urgent": False,
            "sync_status": "error",
            "last_update": datetime.now().isoformat()
        })


def get_notification_urgency(notification_type: str) -> str:
    """Determine urgency level for sidebar notifications."""
    high_urgency = ["deletion_approval_needed", "import_errors", "sync_error"]
    medium_urgency = ["files_detected", "orphaned_documents"]
    
    if notification_type in high_urgency:
        return "high"
    elif notification_type in medium_urgency:
        return "medium"
    else:
        return "low"


def get_notification_icon(notification_type: str) -> str:
    """Get icon for notification type."""
    icons = {
        "files_detected": "📁",
        "files_imported": "✅",
        "deletion_approval_needed": "🗑️",
        "orphaned_documents": "🔍",
        "import_errors": "⚠️",
        "sync_complete_no_changes": "✅",
        "sync_error": "❌"
    }
    return icons.get(notification_type, "🔔")


def requires_action(notification_type: str) -> bool:
    """Check if notification requires admin action."""
    action_required = ["deletion_approval_needed", "orphaned_documents", "import_errors"]
    return notification_type in action_required


def get_change_count(details: dict) -> int:
    """Extract change count from notification details."""
    return (
        details.get('new_files_detected', 0) +
        details.get('deleted_files_detected', 0) +
        details.get('orphaned_documents_detected', 0) +
        details.get('import_errors', 0)
    )


def get_quick_actions(notification_type: str, details: dict) -> list:
    """Get quick action buttons for notification."""
    actions = []
    
    if notification_type == "files_detected":
        actions = [
            {"label": "Sync Now", "action": "manual_sync", "style": "primary"},
            {"label": "Review", "action": "view_files", "style": "secondary"}
        ]
    elif notification_type == "deletion_approval_needed":
        actions = [
            {"label": "Review Deletions", "action": "review_deletions", "style": "warning"},
            {"label": "Approve All", "action": "approve_deletions", "style": "danger"}
        ]
    elif notification_type == "import_errors":
        actions = [
            {"label": "Retry Import", "action": "retry_import", "style": "primary"},
            {"label": "View Errors", "action": "view_errors", "style": "secondary"}
        ]
    
    return actions


@app.get("/api/sync/notifications")
async def get_admin_notifications(
    request: Request, 
    user=CurrentUser,
    days: int = Query(default=7, description="Number of days to look back"),
    unacknowledged_only: bool = Query(default=False, description="Only return unacknowledged notifications")
):
    """Get recent admin notifications for sync issues and manual intervention needs."""
    try:
        notifications = get_recent_notifications(days=days, unacknowledged_only=unacknowledged_only)
        
        # Get summary statistics
        total_notifications = len(notifications)
        unacknowledged_count = len([n for n in notifications if not n.get("acknowledged", False)])
        
        # Group by type for summary
        by_type = {}
        for notification in notifications:
            notif_type = notification.get("type", "unknown")
            if notif_type not in by_type:
                by_type[notif_type] = 0
            by_type[notif_type] += 1
        
        return JSONResponse(content={
            "notifications": notifications,
            "summary": {
                "total_notifications": total_notifications,
                "unacknowledged_count": unacknowledged_count,
                "days_requested": days,
                "notifications_by_type": by_type
            },
            "enhanced_sync_config": {
                "auto_import_enabled": settings.AUTO_IMPORT_ENABLED,
                "auto_delete_enabled": settings.AUTO_DELETE_ENABLED,
                "manual_approval_required": settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
                "notification_email": settings.AUTO_SYNC_NOTIFICATION_EMAIL,
                "error_threshold": settings.AUTO_SYNC_ERROR_THRESHOLD
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting admin notifications: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to get admin notifications."
        )


@app.post("/api/sync/notifications/{notification_id}/acknowledge")
async def acknowledge_notification(
    notification_id: str,
    request: Request,
    user=CurrentUser
):
    """Acknowledge a specific notification by ID."""
    try:
        # This is a simplified implementation - in production you'd want to 
        # find the specific notification by ID and mark it as acknowledged
        logger.info(f"User {user.get('email', 'unknown')} acknowledged notification {notification_id}")
        
        return JSONResponse(content={
            "message": f"Notification {notification_id} acknowledged",
            "acknowledged_by": user.get('email', 'unknown'),
            "acknowledged_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error acknowledging notification: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to acknowledge notification."
        )


@app.get("/api/sync/settings")
async def get_sync_settings(request: Request, user=CurrentUser):
    """Get current enhanced sync configuration settings."""
    try:
        return JSONResponse(content={
            "enhanced_sync_settings": {
                "auto_sync_enabled": settings.AUTO_SYNC_ENABLED,
                "sync_interval_minutes": settings.SYNC_INTERVAL_MINUTES,
                "auto_import_enabled": settings.AUTO_IMPORT_ENABLED,
                "auto_delete_enabled": settings.AUTO_DELETE_ENABLED,
                "max_files_per_sync": settings.MAX_FILES_PER_SYNC,
                "max_file_size_auto_import_mb": settings.MAX_FILE_SIZE_AUTO_IMPORT // (1024 * 1024),
                "auto_sync_require_manual_approval": settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
                "auto_sync_notification_email": settings.AUTO_SYNC_NOTIFICATION_EMAIL,
                "auto_sync_error_threshold": settings.AUTO_SYNC_ERROR_THRESHOLD,
                "sync_target_drive_id": settings.SYNC_TARGET_DRIVE_ID,
                "sync_target_folder_path": settings.SYNC_TARGET_FOLDER_PATH
            },
            "system_info": {
                "use_sharepoint": settings.USE_SHAREPOINT,
                "file_size_limit_mb": settings.FILE_SIZE_LIMIT // (1024 * 1024),
                "resource_monitoring_available": PSUTIL_AVAILABLE,
                "current_resource_status": check_system_resources()
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting sync settings: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to get sync settings."
        )


def update_env_file(env_updates: dict):
    """Update .env file with new settings while preserving existing variables."""
    import os
    
    env_file_path = ".env"
    
    try:
        # Read existing .env file
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        else:
            lines = []
        
        # Create a dict to track which variables we've updated
        updated_vars = set()
        
        # Process existing lines
        for i, line in enumerate(lines):
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                continue
            
            # Check if this line contains a variable we want to update
            for env_var, new_value in env_updates.items():
                if line.strip().startswith(f"{env_var}="):
                    # Update this line
                    lines[i] = f"{env_var}={new_value}\n"
                    updated_vars.add(env_var)
                    break
        
        # Add any new variables that weren't found in the existing file
        for env_var, new_value in env_updates.items():
            if env_var not in updated_vars:
                # Ensure the last line ends with newline before adding new ones
                if lines and not lines[-1].endswith('\n'):
                    lines[-1] += '\n'
                lines.append(f"{env_var}={new_value}\n")
        
        # Write the updated .env file
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        logger.info(f"Successfully updated .env file with: {list(env_updates.keys())}")
        
    except Exception as e:
        logger.error(f"Failed to update .env file: {e}", exc_info=True)
        raise


@app.post("/api/sync/settings/update")
async def update_sync_settings(
    request: Request,
    user=CurrentUser,
    auto_sync_enabled: bool = Query(None, description="Enable automatic synchronization"),
    auto_import_enabled: bool = Query(None, description="Enable automatic file imports"),
    auto_delete_enabled: bool = Query(None, description="Enable automatic deletion of orphaned documents"),
    max_files_per_sync: int = Query(None, description="Maximum files to process per sync cycle"),
    max_file_size_auto_import_mb: int = Query(None, description="Maximum file size for auto import (MB)"),
    auto_sync_require_manual_approval: bool = Query(None, description="Require manual approval for deletions"),
    auto_sync_notification_email: str = Query(None, description="Email for notifications"),
    auto_sync_error_threshold: int = Query(None, description="Error threshold before disabling sync"),
    sync_interval_minutes: int = Query(None, description="Sync interval in minutes")
):
    """Update enhanced sync settings (runtime configuration)."""
    try:
        updated_settings = {}
        changes_made = []
        
        # Validate and apply setting changes
        if auto_sync_enabled is not None:
            settings.AUTO_SYNC_ENABLED = auto_sync_enabled
            updated_settings["auto_sync_enabled"] = auto_sync_enabled
            changes_made.append(f"Auto sync {'enabled' if auto_sync_enabled else 'disabled'}")
        
        if auto_import_enabled is not None:
            settings.AUTO_IMPORT_ENABLED = auto_import_enabled
            updated_settings["auto_import_enabled"] = auto_import_enabled
            changes_made.append(f"Auto import {'enabled' if auto_import_enabled else 'disabled'}")
        
        if auto_delete_enabled is not None:
            settings.AUTO_DELETE_ENABLED = auto_delete_enabled
            updated_settings["auto_delete_enabled"] = auto_delete_enabled
            changes_made.append(f"Auto delete {'enabled' if auto_delete_enabled else 'disabled'}")
        
        if max_files_per_sync is not None:
            if max_files_per_sync < 1 or max_files_per_sync > 1000:
                raise ValueError("max_files_per_sync must be between 1 and 1000")
            settings.MAX_FILES_PER_SYNC = max_files_per_sync
            updated_settings["max_files_per_sync"] = max_files_per_sync
            changes_made.append(f"Max files per sync set to {max_files_per_sync}")
        
        if max_file_size_auto_import_mb is not None:
            if max_file_size_auto_import_mb < 1 or max_file_size_auto_import_mb > 100:
                raise ValueError("max_file_size_auto_import_mb must be between 1 and 100")
            settings.MAX_FILE_SIZE_AUTO_IMPORT = max_file_size_auto_import_mb * 1024 * 1024
            updated_settings["max_file_size_auto_import_mb"] = max_file_size_auto_import_mb
            changes_made.append(f"Max auto import file size set to {max_file_size_auto_import_mb}MB")
        
        if auto_sync_require_manual_approval is not None:
            settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL = auto_sync_require_manual_approval
            updated_settings["auto_sync_require_manual_approval"] = auto_sync_require_manual_approval
            changes_made.append(f"Manual approval {'required' if auto_sync_require_manual_approval else 'not required'}")
        
        if auto_sync_notification_email is not None:
            # Basic email validation for multiple emails
            if auto_sync_notification_email:
                emails = [email.strip() for email in auto_sync_notification_email.split(',')]
                for email in emails:
                    if email and "@" not in email:
                        raise ValueError(f"Invalid email format: {email}")
                    if email and "." not in email.split("@")[-1]:
                        raise ValueError(f"Invalid email format: {email}")
            settings.AUTO_SYNC_NOTIFICATION_EMAIL = auto_sync_notification_email
            updated_settings["auto_sync_notification_email"] = auto_sync_notification_email
            changes_made.append(f"Notification email(s) set to {auto_sync_notification_email}")
        
        if auto_sync_error_threshold is not None:
            if auto_sync_error_threshold < 1 or auto_sync_error_threshold > 50:
                raise ValueError("auto_sync_error_threshold must be between 1 and 50")
            settings.AUTO_SYNC_ERROR_THRESHOLD = auto_sync_error_threshold
            updated_settings["auto_sync_error_threshold"] = auto_sync_error_threshold
            changes_made.append(f"Error threshold set to {auto_sync_error_threshold}")
        
        if sync_interval_minutes is not None:
            if sync_interval_minutes < 1 or sync_interval_minutes > 1440:  # Max 24 hours
                raise ValueError("sync_interval_minutes must be between 1 and 1440")
            settings.SYNC_INTERVAL_MINUTES = sync_interval_minutes
            updated_settings["sync_interval_minutes"] = sync_interval_minutes
            changes_made.append(f"Sync interval set to {sync_interval_minutes} minutes")
        
        if not changes_made:
            return JSONResponse(content={
                "message": "No settings were updated",
                "current_settings": {
                    "auto_import_enabled": settings.AUTO_IMPORT_ENABLED,
                    "auto_delete_enabled": settings.AUTO_DELETE_ENABLED,
                    "max_files_per_sync": settings.MAX_FILES_PER_SYNC,
                    "max_file_size_auto_import_mb": settings.MAX_FILE_SIZE_AUTO_IMPORT // (1024 * 1024),
                    "auto_sync_require_manual_approval": settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
                    "auto_sync_notification_email": settings.AUTO_SYNC_NOTIFICATION_EMAIL,
                    "auto_sync_error_threshold": settings.AUTO_SYNC_ERROR_THRESHOLD,
                    "sync_interval_minutes": settings.SYNC_INTERVAL_MINUTES
                }
            })
        
        # Persist changes to .env file
        env_updates = {}
        if "auto_sync_enabled" in updated_settings:
            env_updates["AUTO_SYNC_ENABLED"] = str(updated_settings["auto_sync_enabled"]).lower()
        if "auto_import_enabled" in updated_settings:
            env_updates["AUTO_IMPORT_ENABLED"] = str(updated_settings["auto_import_enabled"]).lower()
        if "auto_delete_enabled" in updated_settings:
            env_updates["AUTO_DELETE_ENABLED"] = str(updated_settings["auto_delete_enabled"]).lower()
        if "max_files_per_sync" in updated_settings:
            env_updates["MAX_FILES_PER_SYNC"] = str(updated_settings["max_files_per_sync"])
        if "max_file_size_auto_import_mb" in updated_settings:
            env_updates["MAX_FILE_SIZE_AUTO_IMPORT"] = str(updated_settings["max_file_size_auto_import_mb"] * 1024 * 1024)
        if "auto_sync_require_manual_approval" in updated_settings:
            env_updates["AUTO_SYNC_REQUIRE_MANUAL_APPROVAL"] = str(updated_settings["auto_sync_require_manual_approval"]).lower()
        if "auto_sync_notification_email" in updated_settings:
            env_updates["AUTO_SYNC_NOTIFICATION_EMAIL"] = str(updated_settings["auto_sync_notification_email"])
        if "auto_sync_error_threshold" in updated_settings:
            env_updates["AUTO_SYNC_ERROR_THRESHOLD"] = str(updated_settings["auto_sync_error_threshold"])
        if "sync_interval_minutes" in updated_settings:
            env_updates["SYNC_INTERVAL_MINUTES"] = str(updated_settings["sync_interval_minutes"])
        
        # Update .env file if there are changes
        if env_updates:
            try:
                update_env_file(env_updates)
            except Exception as env_error:
                logger.error(f"Failed to update .env file: {env_error}")
                # Continue execution - don't fail the API call if .env update fails
        
        # Log the changes
        user_email = user.get('email', 'unknown')
        logger.info(f"User {user_email} updated sync settings: {'; '.join(changes_made)}")
        
        # Send notification about settings change
        log_admin_notification(
            "settings_updated",
            f"Sync settings updated by {user_email}: {'; '.join(changes_made)}",
            {
                "updated_by": user_email,
                "changes": changes_made,
                "updated_settings": updated_settings
            }
        )
        
        return JSONResponse(content={
            "message": f"Successfully updated {len(changes_made)} settings",
            "changes_made": changes_made,
            "updated_settings": updated_settings,
            "updated_by": user_email,
            "timestamp": datetime.now().isoformat(),
            "note": "Settings changes take effect immediately for new sync cycles"
        })
        
    except ValueError as ve:
        logger.warning(f"Invalid sync settings update: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error updating sync settings: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to update sync settings."
        )


@app.post("/api/sync/settings/reset")
async def reset_sync_settings(request: Request, user=CurrentUser):
    """Reset enhanced sync settings to safe defaults."""
    try:
        # Reset to safe defaults
        settings.AUTO_IMPORT_ENABLED = False
        settings.AUTO_DELETE_ENABLED = False
        settings.MAX_FILES_PER_SYNC = 50
        settings.MAX_FILE_SIZE_AUTO_IMPORT = 10 * 1024 * 1024  # 10MB
        settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL = True
        settings.AUTO_SYNC_ERROR_THRESHOLD = 5
        
        # Persist reset to .env file
        env_updates = {
            "AUTO_IMPORT_ENABLED": "false",
            "AUTO_DELETE_ENABLED": "false", 
            "MAX_FILES_PER_SYNC": "50",
            "MAX_FILE_SIZE_AUTO_IMPORT": str(10 * 1024 * 1024),
            "AUTO_SYNC_REQUIRE_MANUAL_APPROVAL": "true",
            "AUTO_SYNC_ERROR_THRESHOLD": "5"
        }
        
        try:
            update_env_file(env_updates)
        except Exception as env_error:
            logger.error(f"Failed to update .env file during reset: {env_error}")
        
        user_email = user.get('email', 'unknown')
        logger.info(f"User {user_email} reset sync settings to defaults")
        
        # Send notification
        log_admin_notification(
            "settings_reset",
            f"Sync settings reset to defaults by {user_email}",
            {"reset_by": user_email}
        )
        
        return JSONResponse(content={
            "message": "Sync settings reset to safe defaults",
            "reset_settings": {
                "auto_import_enabled": False,
                "auto_delete_enabled": False,
                "max_files_per_sync": 50,
                "max_file_size_auto_import_mb": 10,
                "auto_sync_require_manual_approval": True,
                "auto_sync_error_threshold": 5
            },
            "reset_by": user_email,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error resetting sync settings: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to reset sync settings."
        )


@app.post("/api/sync/test-email")
async def test_email_notification(request: Request, user=CurrentUser):
    """Send a test email notification to verify email configuration."""
    try:
        # Check if email is configured
        if not settings.AUTO_SYNC_NOTIFICATION_EMAIL:
            raise HTTPException(
                status_code=400, 
                detail="No notification email configured. Please set an email address first."
            )
        
        # Create test notification
        test_notification = {
            "type": "test",
            "message": "This is a test email from DDBrain to verify email notifications are working correctly.",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "test_sent_by": user.get('email') or user.get('username', 'unknown'),
                "system_info": "DDBrain SharePoint RAG System",
                "email_settings": {
                    "smtp_server": settings.SMTP_SERVER,
                    "smtp_port": settings.SMTP_PORT,
                    "email_enabled": settings.EMAIL_ENABLED
                }
            }
        }
        
        # Create test notification
        test_notification = {
            "type": "test",
            "message": "This is a test email from DDBrain to verify email notifications are working correctly.",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "test_sent_by": user.get('email') or user.get('username', 'unknown'),
                "system_info": "DDBrain SharePoint RAG System",
                "email_settings": {
                    "smtp_server": settings.SMTP_SERVER,
                    "smtp_port": settings.SMTP_PORT,
                    "email_enabled": settings.EMAIL_ENABLED
                }
            }
        }
        
        # Send test email directly and check result
        try:
            email_success = send_email_notification(test_notification)
            
            # Also log to sidebar
            _log_to_sidebar_only(
                "test",
                "This is a test email from DDBrain to verify email notifications are working correctly.",
                test_notification["details"]
            )
            
            email_list = [email.strip() for email in settings.AUTO_SYNC_NOTIFICATION_EMAIL.split(',')]
            
            if email_success:
                return JSONResponse(content={
                    "message": f"Test email sent successfully to {len(email_list)} recipient(s)!",
                    "sent_to": email_list,
                    "sent_by": user.get('email') or user.get('username', 'unknown'),
                    "timestamp": datetime.now().isoformat(),
                    "email_enabled": settings.EMAIL_ENABLED,
                    "delivery_status": "success"
                })
            else:
                return JSONResponse(
                    status_code=500,
                    content={
                        "message": "Failed to send test email. Check email configuration and SMTP credentials.",
                        "sent_to": email_list,
                        "sent_by": user.get('email') or user.get('username', 'unknown'),
                        "timestamp": datetime.now().isoformat(),
                        "email_enabled": settings.EMAIL_ENABLED,
                        "smtp_configured": bool(settings.SMTP_EMAIL and settings.SMTP_PASSWORD),
                        "recipient_configured": bool(settings.AUTO_SYNC_NOTIFICATION_EMAIL),
                        "delivery_status": "failed",
                        "suggestion": "Check SMTP credentials, especially the App Password for Gmail"
                    }
                )
            
        except Exception as email_error:
            logger.error(f"Test email failed: {email_error}")
            return JSONResponse(
                status_code=500,
                content={
                    "message": "Failed to send test email. Check email configuration and server logs.",
                    "error": str(email_error),
                    "email_enabled": settings.EMAIL_ENABLED,
                    "smtp_configured": bool(settings.SMTP_EMAIL and settings.SMTP_PASSWORD),
                    "recipient_configured": bool(settings.AUTO_SYNC_NOTIFICATION_EMAIL)
                }
            )
        
    except Exception as e:
        logger.error(f"Error sending test email: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to send test email: {str(e)}"
        )


@app.post("/api/sync/webhook-subscription")
async def create_webhook_subscription(request: Request, user: dict = Depends(get_current_user)):
    """Create a SharePoint webhook subscription for real-time notifications."""
    try:
        # Only allow admin users to create webhook subscriptions
        if not is_admin(user):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Get request parameters
        body = await request.json()
        drive_id = body.get("drive_id", settings.SYNC_TARGET_DRIVE_ID)
        notification_url = body.get("notification_url", settings.WEBHOOK_NOTIFICATION_URL)
        
        if not drive_id:
            raise HTTPException(status_code=400, detail="Drive ID is required")
        
        if not notification_url:
            raise HTTPException(status_code=400, detail="Notification URL is required")
        
        # Get admin token
        token = request.session.get("access_token")
        if not token:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Create webhook subscription
        try:
            subscription = await app.state.sharepoint_client.create_webhook_subscription(
                drive_id=drive_id,
                notification_url=notification_url,
                token=token
            )
            
            # Store subscription details for management
            if not hasattr(app.state, 'webhook_subscriptions'):
                app.state.webhook_subscriptions = {}
            
            app.state.webhook_subscriptions[subscription["id"]] = {
                "subscription_id": subscription["id"],
                "drive_id": drive_id,
                "notification_url": notification_url,
                "expiration_datetime": subscription["expirationDateTime"],
                "created_at": datetime.now().isoformat(),
                "created_by": user.get("email", "unknown"),
                "status": "active"
            }
            
            # Log successful subscription creation
            log_admin_notification(
                "webhook_subscription_created",
                f"✅ SharePoint webhook subscription created successfully",
                {
                    "subscription_id": subscription["id"],
                    "drive_id": drive_id,
                    "notification_url": notification_url,
                    "expiration_datetime": subscription["expirationDateTime"],
                    "created_by": user.get("email", "unknown")
                }
            )
            
            return JSONResponse(content={
                "status": "success",
                "subscription": subscription,
                "message": "Webhook subscription created successfully"
            })
            
        except Exception as e:
            error_msg = f"Failed to create webhook subscription: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # Log failed subscription attempt
            log_admin_notification(
                "webhook_subscription_failed",
                f"❌ Failed to create SharePoint webhook subscription",
                {
                    "error": str(e),
                    "drive_id": drive_id,
                    "notification_url": notification_url,
                    "attempted_by": user.get("email", "unknown")
                }
            )
            
            raise HTTPException(status_code=500, detail=error_msg)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in webhook subscription creation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/sync/webhook-subscriptions")
async def list_webhook_subscriptions(user: dict = Depends(get_current_user)):
    """List all active webhook subscriptions."""
    try:
        # Only allow admin users to view webhook subscriptions
        if not is_admin(user):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        subscriptions = getattr(app.state, 'webhook_subscriptions', {})
        
        # Add status information
        current_time = datetime.now()
        for sub_id, sub_data in subscriptions.items():
            try:
                expiration_time = datetime.fromisoformat(sub_data["expiration_datetime"].replace('Z', '+00:00'))
                if expiration_time.replace(tzinfo=None) < current_time:
                    sub_data["status"] = "expired"
                elif (expiration_time.replace(tzinfo=None) - current_time).days < 1:
                    sub_data["status"] = "expiring_soon"
                else:
                    sub_data["status"] = "active"
            except Exception as e:
                logger.warning(f"Error checking subscription expiration: {e}")
                sub_data["status"] = "unknown"
        
        return JSONResponse(content={
            "subscriptions": subscriptions,
            "total_count": len(subscriptions)
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing webhook subscriptions: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/sync/webhook-subscription/{subscription_id}")
async def delete_webhook_subscription(subscription_id: str, request: Request, user: dict = Depends(get_current_user)):
    """Delete a SharePoint webhook subscription."""
    try:
        # Only allow admin users to delete webhook subscriptions
        if not is_admin(user):
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Get admin token
        token = request.session.get("access_token")
        if not token:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Delete webhook subscription
        try:
            await app.state.sharepoint_client.delete_webhook_subscription(subscription_id, token)
            
            # Remove from local storage
            if hasattr(app.state, 'webhook_subscriptions') and subscription_id in app.state.webhook_subscriptions:
                sub_data = app.state.webhook_subscriptions.pop(subscription_id)
                
                # Log successful deletion
                log_admin_notification(
                    "webhook_subscription_deleted",
                    f"🗑️ SharePoint webhook subscription deleted",
                    {
                        "subscription_id": subscription_id,
                        "drive_id": sub_data.get("drive_id", "unknown"),
                        "deleted_by": user.get("email", "unknown")
                    }
                )
            
            return JSONResponse(content={
                "status": "success",
                "message": "Webhook subscription deleted successfully"
            })
            
        except Exception as e:
            error_msg = f"Failed to delete webhook subscription: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # Log failed deletion attempt
            log_admin_notification(
                "webhook_subscription_delete_failed",
                f"❌ Failed to delete SharePoint webhook subscription",
                {
                    "subscription_id": subscription_id,
                    "error": str(e),
                    "attempted_by": user.get("email", "unknown")
                }
            )
            
            raise HTTPException(status_code=500, detail=error_msg)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in webhook subscription deletion: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/sync/health-dashboard")
async def sync_health_dashboard(request: Request, user=CurrentUser, ms_token: str = MsTokenDep):
    """Comprehensive sync health dashboard with detailed statistics and recommendations."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for sync health dashboard")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Get SharePoint files
        known_working_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"

        sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
        sharepoint_files = await app.state.sharepoint_client.list_files(
            drive_id=known_working_drive_id,
            token=ms_token,
            folder_path=sync_folder
        )

        sharepoint_file_ids = set()
        sharepoint_file_details = {}

        for item in sharepoint_files:
            if "file" in item:
                file_id = item.get("id")
                file_name = item.get("name", "Unknown")
                sharepoint_file_ids.add(file_id)
                sharepoint_file_details[file_id] = {
                    "name": file_name,
                    "size": item.get("size", 0),
                    "last_modified": item.get("lastModifiedDateTime", "Unknown")
                }

        # Analyze Index documents
        indexed_docs = []
        indexed_sp_ids = set()
        orphaned_docs = []
        local_only_docs = []

        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                source = doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"

                if sp_id:
                    indexed_sp_ids.add(sp_id)
                    indexed_docs.append({
                        "doc_id": doc_id,
                        "sharepoint_id": sp_id,
                        "file_name": file_name,
                        "source": source,
                        "in_sharepoint": sp_id in sharepoint_file_ids,
                        "sync_status": "synced" if sp_id in sharepoint_file_ids else "deleted_from_sharepoint"
                    })
                else:
                    if source == "local_upload" or source == "manual_upload":
                        local_only_docs.append({
                            "doc_id": doc_id,
                            "file_name": file_name,
                            "source": source
                        })
                    else:
                        orphaned_docs.append({
                            "doc_id": doc_id,
                            "file_name": file_name,
                            "source": source
                        })

        # Calculate discrepancies
        missing_from_index = sharepoint_file_ids - indexed_sp_ids
        missing_from_sharepoint = indexed_sp_ids - sharepoint_file_ids

        missing_from_index_details = [
            {
                "sharepoint_id": sp_id,
                "file_name": sharepoint_file_details.get(sp_id, {}).get("name", "Unknown"),
                "size": sharepoint_file_details.get(sp_id, {}).get("size", 0),
                "last_modified": sharepoint_file_details.get(sp_id, {}).get("last_modified", "Unknown")
            }
            for sp_id in missing_from_index
        ]

        missing_from_sharepoint_details = [
            doc for doc in indexed_docs if doc["sharepoint_id"] in missing_from_sharepoint
        ]

        # Calculate sync health score (0-100)
        total_issues = len(missing_from_index) + len(missing_from_sharepoint) + len(orphaned_docs)
        total_documents = len(sharepoint_file_ids) + len(indexed_docs) + len(orphaned_docs) + len(local_only_docs)
        sync_health_score = max(0, 100 - (total_issues * 100 / max(total_documents, 1)))

        # Determine overall sync status
        if sync_health_score >= 95:
            sync_status = "excellent"
        elif sync_health_score >= 80:
            sync_status = "good"
        elif sync_health_score >= 60:
            sync_status = "fair"
        else:
            sync_status = "poor"

        # Generate recommendations
        recommendations = []
        if len(missing_from_index) > 0:
            recommendations.append(f"Run manual sync to import {len(missing_from_index)} new SharePoint files")
        if len(missing_from_sharepoint) > 0:
            recommendations.append(f"Run manual sync to remove {len(missing_from_sharepoint)} deleted SharePoint files")
        if len(orphaned_docs) > 0:
            recommendations.append(f"Run orphaned document cleanup to remove {len(orphaned_docs)} orphaned documents")
        if len(recommendations) == 0:
            recommendations.append("Sync is healthy - no action needed")

        # Get background sync info
        background_sync_info = {
            "enabled": settings.AUTO_SYNC_ENABLED,
            "interval_minutes": settings.SYNC_INTERVAL_MINUTES,
            "last_run": getattr(app.state, 'last_sync_time', None),
            "last_stats": getattr(app.state, 'last_sync_stats', {})
        }

        if background_sync_info["last_run"]:
            background_sync_info["last_run"] = background_sync_info["last_run"].isoformat()

        return JSONResponse(content={
            "sync_health": {
                "status": sync_status,
                "score": round(sync_health_score, 1),
                "total_issues": total_issues
            },
            "statistics": {
                "sharepoint_files": len(sharepoint_file_ids),
                "indexed_documents": len(indexed_docs),
                "local_only_documents": len(local_only_docs),
                "orphaned_documents": len(orphaned_docs),
                "missing_from_index": len(missing_from_index),
                "missing_from_sharepoint": len(missing_from_sharepoint),
                "total_documents": total_documents
            },
            "discrepancies": {
                "missing_from_index": missing_from_index_details[:10],  # First 10
                "missing_from_sharepoint": missing_from_sharepoint_details[:10],  # First 10
                "orphaned_documents": orphaned_docs[:10],  # First 10
                "local_only_documents": local_only_docs[:10]  # First 10
            },
            "recommendations": recommendations,
            "background_sync": background_sync_info,
            "actions_available": {
                "manual_sync": "/api/sync/manual",
                "cleanup_orphaned": "/api/documents/cleanup-orphaned",
                "clear_and_resync": "/api/documents/clear",
                "verify_sync": "/api/sync/verify"
            }
        })

    except Exception as e:
        logger.error(f"Error generating sync health dashboard: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to generate sync health dashboard."
        )


@app.post("/api/sync/full-background")
async def trigger_full_background_sync(request: Request, user=CurrentUser, ms_token: str = MsTokenDep):
    """Triggers a comprehensive background sync that includes imports and deletions."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for full background sync")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        logger.info("=== FULL BACKGROUND SYNC TRIGGERED ===")

        # Use the same logic as manual sync but with background sync metadata
        known_working_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"

        # Get files from SharePoint
        sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
        files_list = await app.state.sharepoint_client.list_files(
            drive_id=known_working_drive_id,
            token=ms_token,
            folder_path=sync_folder
        )

        logger.info(f"Full background sync found {len(files_list)} items in {sync_folder}")

        # Track current SharePoint file IDs
        current_sp_ids = set()
        files_found = 0
        files_imported = 0
        import_errors = []

        # Process files for import
        async def process_sharepoint_items(items, folder_path=""):
            nonlocal files_found, files_imported, current_sp_ids

            for item in items:
                if item.get("file"):
                    files_found += 1
                    file_id = item.get("id")
                    file_name = item.get("name", "Unknown")
                    current_sp_ids.add(file_id)

                    # Check if already indexed
                    existing_docs = []
                    if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                        for doc_id, doc_info in app.state.index.docstore.docs.items():
                            if doc_info.metadata and doc_info.metadata.get("sharepoint_id") == file_id:
                                existing_docs.append(doc_info)

                    if not existing_docs:
                        # Import new file
                        try:
                            logger.info(f"Full background sync importing: {file_name}")

                            # Download and process file
                            file_content = await app.state.sharepoint_client.download_file_content(
                                drive_id=known_working_drive_id,
                                file_id=file_id,
                                token=ms_token
                            )

                            # Create temporary file
                            temp_file_path = Path(f"/tmp/bg_sync_{file_id}_{file_name}")
                            with open(temp_file_path, "wb") as f:
                                f.write(file_content)

                            try:
                                # Process the file
                                from llama_index.core import SimpleDirectoryReader
                                reader = SimpleDirectoryReader(input_files=[temp_file_path])
                                documents = reader.load_data()

                                # Fix LlamaIndex default exclusions to allow file_name in embeddings
                                for doc in documents:
                                    doc.excluded_embed_metadata_keys = []
                                    doc.excluded_llm_metadata_keys = []

                                # Add enhanced metadata for background sync
                                for doc in documents:
                                    doc.metadata.update({
                                        "sharepoint_id": file_id,
                                        "file_name": file_name,
                                        "source": "background_sync",
                                        "drive_id": known_working_drive_id,
                                        "web_url": f"https://graph.microsoft.com/v1.0/drives/{known_working_drive_id}/items/{file_id}",
                                        "created_datetime": datetime.now().isoformat(),
                                        "sync_imported": True,
                                        "sync_type": "full_background",
                                        "sync_timestamp": datetime.now().isoformat()
                                    })

                                # Add documents to index
                                for doc in documents:
                                    app.state.index.insert(doc)

                                files_imported += 1
                                logger.info(f"✓ Background sync imported: {file_name}")

                            finally:
                                # Clean up temp file
                                if temp_file_path.exists():
                                    temp_file_path.unlink()

                        except Exception as e:
                            error_msg = f"Failed to import {file_name}: {str(e)}"
                            logger.error(error_msg)
                            import_errors.append(error_msg)
                    else:
                        logger.debug(f"File already indexed: {file_name}")

                elif item.get("folder"):
                    # Process subfolders recursively
                    try:
                        subfolder_path = f"{folder_path}/{item.get('name')}" if folder_path else item.get('name')
                        subfolder_files = await app.state.sharepoint_client.list_files(
                            drive_id=known_working_drive_id,
                            token=ms_token,
                            folder_path=f"{sync_folder}/{subfolder_path}"
                        )
                        await process_sharepoint_items(subfolder_files, subfolder_path)
                    except Exception as e:
                        logger.warning(f"Background sync couldn't process subfolder {item.get('name')}: {e}")

        # Process all items
        await process_sharepoint_items(files_list)

        # Enhanced deletion logic with orphaned document cleanup
        indexed_sp_ids = {}
        orphaned_docs = []

        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                if sp_id:
                    indexed_sp_ids[sp_id] = doc_id
                else:
                    # Track orphaned documents
                    file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                    orphaned_docs.append({
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                    })

        # Delete documents no longer in SharePoint
        ids_in_index = set(indexed_sp_ids.keys())
        ids_to_delete = ids_in_index - current_sp_ids
        deleted_count = 0
        orphaned_deleted_count = 0
        errors = []

        if ids_to_delete:
            logger.info(f"Full background sync removing {len(ids_to_delete)} deleted files")
            for sp_id_to_delete in ids_to_delete:
                doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                # Use comprehensive SharePoint document deletion (fixes all nodes for the document)
                deletion_success = await delete_sharepoint_document_completely(sp_id_to_delete)
                if deletion_success:
                    logger.info(f"Background sync successfully deleted: SharePoint ID {sp_id_to_delete}")
                    deleted_count += 1
                else:
                    error_msg = f"Failed to delete and verify SharePoint ID {sp_id_to_delete}"
                    logger.error(error_msg)
                    errors.append(error_msg)

        # Remove orphaned documents
        if orphaned_docs and settings.USE_SHAREPOINT:
            logger.info(f"Full background sync removing {len(orphaned_docs)} orphaned documents")
            for orphaned_doc in orphaned_docs:
                doc_id = orphaned_doc["doc_id"]
                # Use improved deletion with verification
                deletion_success = await delete_document_with_verification(doc_id)
                if deletion_success:
                    logger.info(f"Background sync successfully deleted orphaned: {orphaned_doc['file_name']}")
                    orphaned_deleted_count += 1
                else:
                    error_msg = f"Failed to delete and verify orphaned document {orphaned_doc['file_name']}"
                    logger.error(error_msg)
                    errors.append(error_msg)

        # Persist changes
        if files_imported > 0 or deleted_count > 0 or orphaned_deleted_count > 0:
            logger.info("Persisting full background sync changes...")
            await persist_index()
            logger.info("Full background sync persistence complete.")

        # Update sync statistics
        total_deleted = deleted_count + orphaned_deleted_count
        app.state.last_sync_time = datetime.now()
        app.state.last_sync_stats = {
            "sync_type": "full_background",
            "files_imported": files_imported,
            "deleted_count": deleted_count,
            "orphaned_deleted_count": orphaned_deleted_count,
            "total_deleted": total_deleted,
            "import_errors": len(import_errors),
            "deletion_errors": len(errors),
            "sync_needed": False  # Full sync completed
        }

        logger.info("=== FULL BACKGROUND SYNC COMPLETE ===")

        return JSONResponse(content={
            "status": "full_background_sync_completed",
            "message": f"Full background sync completed: imported {files_imported} files, deleted {total_deleted} documents ({deleted_count} SharePoint deletions, {orphaned_deleted_count} orphaned cleanups)",
            "statistics": {
                "sharepoint_files_found": files_found,
                "files_imported": files_imported,
                "deleted_count": deleted_count,
                "orphaned_deleted_count": orphaned_deleted_count,
                "total_deleted": total_deleted,
                "orphaned_documents_found": len(orphaned_docs)
            },
            "errors": {
                "import_errors": import_errors,
                "deletion_errors": errors
            },
            "sync_timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error during full background sync: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to complete full background sync."
        )


@app.post("/api/sync/webhook-notification")
async def handle_sharepoint_webhook(request: Request):
    """Handle SharePoint webhook notifications for real-time sync triggers."""
    try:
        # Check for validation query parameter (used by Microsoft Graph to validate endpoint)
        validation_token = request.query_params.get("validationToken")
        if validation_token:
            logger.info(f"Webhook endpoint validation requested with token: {validation_token}")
            return PlainTextResponse(content=validation_token, status_code=200)
        
        # Parse webhook payload
        body = await request.body()
        webhook_data = json.loads(body) if body else {}

        logger.info(f"Received SharePoint webhook notification: {webhook_data}")

        # Enhanced webhook validation
        if not webhook_data.get("value"):
            logger.warning("Invalid webhook payload - no 'value' field")
            return JSONResponse(content={"status": "ignored", "reason": "invalid_payload"})
        
        # Validate client state if configured
        client_state = request.headers.get("X-MS-CLIENT-STATE")
        expected_client_state = "DDBSharepointWebhookSecret"
        if client_state != expected_client_state:
            logger.warning(f"Invalid client state: expected '{expected_client_state}', got '{client_state}'")
            return JSONResponse(content={"status": "ignored", "reason": "invalid_client_state"}, status_code=401)

        # Process webhook notifications
        notifications_processed = 0
        for notification in webhook_data.get("value", []):
            resource = notification.get("resource", "")
            change_type = notification.get("changeType", "")
            client_state = notification.get("clientState", "")

            logger.info(f"Processing webhook: {change_type} for resource {resource}")
            
            # Additional security validation
            if client_state and client_state != "DDBSharepointWebhookSecret":
                logger.warning(f"Notification with invalid client state: {client_state}")
                continue

            # Enhanced real-time change processing
            if change_type in ["created", "updated", "deleted"] and "driveItem" in resource:
                # Store notification for background processing
                if not hasattr(app.state, 'pending_webhook_notifications'):
                    app.state.pending_webhook_notifications = []

                webhook_notification = {
                    "timestamp": datetime.now().isoformat(),
                    "change_type": change_type,
                    "resource": resource,
                    "notification_id": notification.get("id", "unknown")
                }
                
                app.state.pending_webhook_notifications.append(webhook_notification)
                
                # Send immediate notification to sidebar
                log_admin_notification(
                    "webhook_change_detected",
                    f"🔄 Real-time change detected: {change_type} in SharePoint",
                    {
                        "change_type": change_type,
                        "resource": resource,
                        "webhook_timestamp": datetime.now().isoformat(),
                        "immediate_notification": True,
                        "sync_triggered": True
                    }
                )

                notifications_processed += 1

                # Trigger immediate background sync check if enabled
                if settings.AUTO_SYNC_ENABLED:
                    # Update last sync stats to indicate webhook-triggered sync needed
                    app.state.last_sync_stats = getattr(app.state, 'last_sync_stats', {})
                    app.state.last_sync_stats.update({
                        "webhook_triggered": True,
                        "webhook_change_type": change_type,
                        "sync_needed": True,
                        "webhook_timestamp": datetime.now().isoformat()
                    })
                    
                    # Trigger immediate sync in background task
                    logger.info(f"Webhook triggered immediate sync for {change_type} change")
                    asyncio.create_task(webhook_triggered_sync(change_type, resource))
                else:
                    logger.info("Webhook received but auto-sync is disabled")

        logger.info(f"Processed {notifications_processed} webhook notifications")

        return JSONResponse(content={
            "status": "processed",
            "notifications_processed": notifications_processed,
            "sync_triggered": notifications_processed > 0 and settings.AUTO_SYNC_ENABLED
        })

    except Exception as e:
        logger.error(f"Error processing SharePoint webhook: {e}", exc_info=True)
        return JSONResponse(
            content={"status": "error", "message": str(e)},
            status_code=500
        )


@app.get("/api/sync/webhook-health")
async def get_webhook_health():
    """Comprehensive webhook health monitoring endpoint."""
    try:
        webhook_health = {
            "webhook_endpoint": settings.WEBHOOK_NOTIFICATION_URL if hasattr(settings, 'WEBHOOK_NOTIFICATION_URL') else "Not configured",
            "webhooks_enabled": settings.ENABLE_SHAREPOINT_WEBHOOKS if hasattr(settings, 'ENABLE_SHAREPOINT_WEBHOOKS') else False,
            "last_webhook_received": "Never",
            "webhook_notifications_today": 0,
            "pending_notifications": 0,
            "subscription_status": "Unknown",
            "health_status": "healthy"
        }
        
        # Check recent webhook activity
        if hasattr(app.state, 'pending_webhook_notifications'):
            webhook_health["pending_notifications"] = len(app.state.pending_webhook_notifications)
            
            # Find most recent webhook
            if app.state.pending_webhook_notifications:
                last_webhook = max(app.state.pending_webhook_notifications, 
                                 key=lambda x: x.get('timestamp', ''))
                webhook_health["last_webhook_received"] = last_webhook.get('timestamp', 'Unknown')
        
        # Count today's webhook notifications from logs
        today = datetime.now().strftime('%Y-%m-%d')
        notifications = get_recent_notifications(days=1)
        webhook_notifications_today = len([n for n in notifications 
                                         if n.get('type') == 'webhook_change_detected' 
                                         and n.get('timestamp', '').startswith(today)])
        webhook_health["webhook_notifications_today"] = webhook_notifications_today
        
        # Check subscription status
        if hasattr(app.state, 'sharepoint_subscription_id'):
            webhook_health["subscription_status"] = "Active"
            webhook_health["subscription_id"] = app.state.sharepoint_subscription_id
        else:
            webhook_health["subscription_status"] = "Not registered"
            webhook_health["health_status"] = "warning"
        
        # Determine overall health
        if webhook_health["subscription_status"] == "Not registered":
            webhook_health["health_status"] = "warning"
            webhook_health["recommendation"] = "Register webhook subscription for real-time notifications"
        elif webhook_health["webhook_notifications_today"] == 0:
            webhook_health["health_status"] = "info"
            webhook_health["recommendation"] = "No webhook activity today - this is normal if no SharePoint changes occurred"
        else:
            webhook_health["health_status"] = "healthy"
            webhook_health["recommendation"] = "Webhook system operating normally"
        
        return JSONResponse(content=webhook_health)
        
    except Exception as e:
        logger.error(f"Error getting webhook health: {e}", exc_info=True)
        return JSONResponse(content={
            "health_status": "error",
            "error": str(e),
            "recommendation": "Check application logs for webhook issues"
        })


@app.post("/api/sync/test-notification-system")
async def test_comprehensive_notification_system(user=CurrentUser):
    """Comprehensive test of the entire notification system including email and sidebar."""
    try:
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests_run": [],
            "tests_passed": 0,
            "tests_failed": 0,
            "overall_status": "unknown"
        }
        
        # Test 1: Email configuration
        test_results["tests_run"].append("email_configuration")
        email_configured = bool(
            settings.EMAIL_ENABLED and 
            settings.SMTP_EMAIL and 
            settings.SMTP_PASSWORD and 
            settings.AUTO_SYNC_NOTIFICATION_EMAIL
        )
        
        if email_configured:
            test_results["tests_passed"] += 1
            test_results["email_config_status"] = "✅ Configured"
        else:
            test_results["tests_failed"] += 1
            test_results["email_config_status"] = "❌ Missing configuration"
        
        # Test 2: Send test email
        test_results["tests_run"].append("email_delivery")
        test_notification = {
            "type": "notification_system_test",
            "message": "🧪 Comprehensive notification system test - All systems operational",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "test_type": "comprehensive_system_test",
                "tested_by": user.get('email', 'unknown'),
                "components_tested": ["email", "sidebar", "logging", "webhooks"]
            }
        }
        
        email_success = send_email_notification(test_notification)
        if email_success:
            test_results["tests_passed"] += 1
            test_results["email_delivery_status"] = "✅ Email sent successfully"
        else:
            test_results["tests_failed"] += 1
            test_results["email_delivery_status"] = "❌ Email delivery failed"
        
        # Test 3: Sidebar notification
        test_results["tests_run"].append("sidebar_notification")
        try:
            log_admin_notification(
                "system_test_notification",
                "🧪 Sidebar notification test - System health check complete",
                {
                    "test_type": "sidebar_test",
                    "tested_by": user.get('email', 'unknown'),
                    "test_timestamp": datetime.now().isoformat()
                }
            )
            test_results["tests_passed"] += 1
            test_results["sidebar_notification_status"] = "✅ Sidebar notification created"
        except Exception as e:
            test_results["tests_failed"] += 1
            test_results["sidebar_notification_status"] = f"❌ Sidebar notification failed: {e}"
        
        # Test 4: Webhook health
        test_results["tests_run"].append("webhook_system")
        webhook_enabled = hasattr(settings, 'ENABLE_SHAREPOINT_WEBHOOKS') and settings.ENABLE_SHAREPOINT_WEBHOOKS
        webhook_url_configured = hasattr(settings, 'WEBHOOK_NOTIFICATION_URL') and settings.WEBHOOK_NOTIFICATION_URL
        
        if webhook_enabled and webhook_url_configured:
            test_results["tests_passed"] += 1
            test_results["webhook_status"] = "✅ Webhook system configured"
        else:
            test_results["tests_failed"] += 1
            test_results["webhook_status"] = "⚠️ Webhook system not fully configured"
        
        # Test 5: Background sync status
        test_results["tests_run"].append("background_sync")
        sync_enabled = settings.AUTO_SYNC_ENABLED
        if sync_enabled and hasattr(app.state, 'last_sync_stats'):
            test_results["tests_passed"] += 1
            test_results["background_sync_status"] = "✅ Background sync active"
        elif sync_enabled:
            test_results["tests_failed"] += 1
            test_results["background_sync_status"] = "⚠️ Background sync enabled but no stats available"
        else:
            test_results["tests_failed"] += 1
            test_results["background_sync_status"] = "❌ Background sync disabled"
        
        # Test 6: File system permissions
        test_results["tests_run"].append("file_permissions")
        try:
            test_file = Path("storage/data/test_permissions.txt")
            test_file.parent.mkdir(parents=True, exist_ok=True)
            test_file.write_text("test")
            test_file.unlink()
            test_results["tests_passed"] += 1
            test_results["file_permissions_status"] = "✅ File system writable"
        except Exception as e:
            test_results["tests_failed"] += 1
            test_results["file_permissions_status"] = f"❌ File system error: {e}"
        
        # Calculate overall status
        total_tests = len(test_results["tests_run"])
        if test_results["tests_failed"] == 0:
            test_results["overall_status"] = "✅ ALL SYSTEMS OPERATIONAL"
        elif test_results["tests_passed"] > test_results["tests_failed"]:
            test_results["overall_status"] = "⚠️ MOSTLY OPERATIONAL (some issues detected)"
        else:
            test_results["overall_status"] = "❌ MULTIPLE SYSTEM ISSUES DETECTED"
        
        test_results["summary"] = f"Passed: {test_results['tests_passed']}/{total_tests} | Failed: {test_results['tests_failed']}/{total_tests}"
        
        return JSONResponse(content=test_results)
        
    except Exception as e:
        logger.error(f"Error running comprehensive notification test: {e}", exc_info=True)
        return JSONResponse(content={
            "overall_status": "❌ TEST SYSTEM FAILURE",
            "error": str(e),
            "recommendation": "Check application logs for detailed error information"
        }, status_code=500)


@app.post("/api/sync/manual")
async def manual_sync(request: Request, user=CurrentUser):
    """Discover all drives and find the configured folder for manual sync."""
    logger.error("🚨🚨🚨🚨🚨 MANUAL SYNC ENDPOINT CALLED - ULTRA VISIBLE LOGGING! 🚨🚨🚨🚨🚨")
    logger.error(f"🔍 This confirms manual sync is being triggered")
    logger.info("=== MANUAL SYNC ENDPOINT CALLED ===")
    logger.info(f"Request URL: {request.url}")
    logger.info(f"Request method: {request.method}")
    # Initialize diagnostic information containers
    accessible_sites = []
    accessible_drives = []
    site_access_errors = []
    drive_access_errors = []
    
    try:
        # Check if we have the necessary components
        if not hasattr(app.state, "index") or not app.state.index:
            raise HTTPException(status_code=503, detail="Index is not ready.")
        
        if not hasattr(app.state, "sharepoint_client"):
            raise HTTPException(status_code=503, detail="SharePoint client is not configured.")
        
        if not settings.USE_SHAREPOINT:
            raise HTTPException(status_code=400, detail="SharePoint integration is not enabled.")
        
        # Get Microsoft access token
        try:
            ms_token = await get_microsoft_access_token(request)
            if not ms_token:
                return {
                    "status": "error",
                    "message": "Microsoft access token not available",
                    "detail": "Please login with Microsoft again"
                }
            logger.info("=== STARTING DRIVE DISCOVERY AND MANUAL SYNC ===")
            logger.info(f"Microsoft token obtained (length: {len(ms_token)})")
        except Exception as e:
            logger.error(f"Failed to get Microsoft access token: {e}")
            return {
                "status": "error", 
                "message": f"Authentication failed: {str(e)}",
                "detail": str(e)
            }
        
        # FIRST: Check what sites are actually accessible
        logger.info("=== CHECKING ACCESSIBLE SITES ===")
        try:
            sites = await app.state.sharepoint_client.list_sites(ms_token)
            logger.info(f"Found {len(sites)} accessible sites:")
            for site in sites:
                site_info = {
                    "display_name": site.get('displayName', 'Unknown'),
                    "web_url": site.get('webUrl', 'No URL'),
                    "id": site.get('id', 'Unknown'),
                    "description": site.get('description', ''),
                    "site_collection_hostname": site.get('siteCollection', {}).get('hostname', ''),
                    "created_date": site.get('createdDateTime', ''),
                    "last_modified": site.get('lastModifiedDateTime', '')
                }
                accessible_sites.append(site_info)
                logger.info(f"  Site: {site_info['display_name']} - {site_info['web_url']}")
                logger.info(f"    ID: {site_info['id']}")
                
        except Exception as e:
            error_detail = {
                "error_type": "site_access_failure",
                "error_message": str(e),
                "error_location": "list_sites"
            }
            site_access_errors.append(error_detail)
            logger.error(f"Failed to list sites: {e}")
            return {
                "status": "error",
                "message": f"Cannot access SharePoint sites: {str(e)}",
                "detail": str(e),
                "diagnostic_info": {
                    "accessible_sites": accessible_sites,
                    "accessible_drives": accessible_drives,
                    "site_access_errors": site_access_errors,
                    "drive_access_errors": drive_access_errors
                }
            }
        
        # SIMPLIFIED SYNC: Use same approach as working Import functionality
        logger.info("=== SIMPLIFIED MANUAL SYNC USING WORKING DRIVE ===")
        # Use the correct working drive ID instead of the malformed environment variable
        known_working_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
        logger.info(f"Using verified working drive ID: {known_working_drive_id}")
        
        # Use direct approach - list files from the configured folder
        try:
            # Get all files from the configured folder using the same method as Import
            sync_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
            logger.info("=== SYNC PATH DIAGNOSTICS ===")
            logger.info(f"Configured SYNC_TARGET_FOLDER_PATH: '{settings.SYNC_TARGET_FOLDER_PATH}'")
            logger.info(f"Using sync folder: '{sync_folder}'")
            logger.info(f"Using drive ID: {known_working_drive_id}")
            logger.info(f"SharePoint site: {app.state.sharepoint_client.site_name}")
            logger.info("=== END SYNC PATH DIAGNOSTICS ===")
            
            # Use the list_files method with folder_path to get configured folder contents
            files_list = await app.state.sharepoint_client.list_files(
                drive_id=known_working_drive_id, 
                token=ms_token, 
                folder_path=sync_folder
            )
            
            logger.info(f"Found {len(files_list)} items in {sync_folder}")
            
            # Process each item (files and folders)
            files_found = 0
            files_imported = 0
            import_errors = []
            current_sp_ids = set()
            
            # Get all indexed docs for deletion detection
            all_indexed_docs = []
            if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                all_indexed_docs = list(app.state.index.docstore.docs.values())
            
            # Helper function to import a file (same as before)
            async def import_sharepoint_file(drive_id, file_id, file_name, token):
                """Import a single file from SharePoint into the index."""
                try:
                    # Download the file
                    file_content = await app.state.sharepoint_client.download_file_content(drive_id, file_id, token)
                    
                    # Create a temporary file
                    import tempfile
                    import os
                    temp_dir = os.path.join(os.getcwd(), "temp")
                    os.makedirs(temp_dir, exist_ok=True)
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file_name}", dir=temp_dir) as temp_file:
                        temp_file.write(file_content)
                        temp_file_path = temp_file.name
                    
                    # Process the file using existing document processing
                    from llama_index.core import SimpleDirectoryReader
                    from pathlib import Path
                    
                    # Use SimpleDirectoryReader to load the file
                    reader = SimpleDirectoryReader(input_files=[temp_file_path])
                    documents = reader.load_data()
                    
                    # Fix LlamaIndex default exclusions to allow file_name in embeddings
                    for doc in documents:
                        doc.excluded_embed_metadata_keys = []
                        doc.excluded_llm_metadata_keys = []
                    
                    # Add SharePoint metadata to each document
                    for doc in documents:
                        doc.metadata.update({
                            "sharepoint_id": file_id,
                            "file_name": file_name,
                            "source": "sharepoint_sync",
                            "drive_id": drive_id,
                            "web_url": f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{file_id}",
                            "created_datetime": datetime.now().isoformat(),
                            "sync_imported": True
                        })
                    
                    # Add documents to the index
                    for doc in documents:
                        app.state.index.insert(doc)
                    
                    # Clean up temp file
                    os.unlink(temp_file_path)
                    
                    # Persist the index
                    app.state.index.storage_context.persist()
                    
                    return len(documents)
                    
                except Exception as e:
                    logger.error(f"Error importing file {file_name}: {e}")
                    raise e
            
            # Process files and folders recursively
            async def process_sharepoint_items(items, folder_path=""):
                """Recursively process SharePoint items (files and folders)."""
                nonlocal files_found, files_imported, current_sp_ids
                
                for item in items:
                    item_id = item.get("id")
                    item_name = item.get("name")
                    
                    if not item_id or not item_name:
                        logger.warning(f"Skipping item with missing ID or name: {item}")
                        continue
                    
                    # If it's a file, process it
                    if not item.get("is_folder"):
                        files_found += 1
                        current_sp_ids.add(item_id)
                        
                        # Check if this file is already indexed
                        existing_docs = [
                            doc for doc in all_indexed_docs 
                            if doc.metadata.get("sharepoint_id") == item_id
                        ]
                        
                        if not existing_docs:
                            # File is new - import it!
                            try:
                                logger.info(f"Importing new file: {item_name} (ID: {item_id})")
                                doc_count = await import_sharepoint_file(known_working_drive_id, item_id, item_name, ms_token)
                                logger.info(f"✓ Successfully imported: {item_name} ({doc_count} documents)")
                                files_imported += 1
                            except Exception as e:
                                logger.error(f"Failed to import {item_name}: {e}")
                                import_errors.append(f"{item_name}: {str(e)}")
                        else:
                            logger.info(f"File already indexed: {item_name}")
                    
                    # If it's a folder, recurse into it
                    elif item.get("is_folder"):
                        logger.info(f"Processing subfolder: {item_name}")
                        subfolder_path = f"{folder_path}/{item_name}" if folder_path else item_name
                        
                        try:
                            # Get files from the subfolder
                            subfolder_files = await app.state.sharepoint_client.list_files(
                                drive_id=known_working_drive_id, 
                                token=ms_token, 
                                folder_path=f"{sync_folder}/{subfolder_path}"
                            )
                            
                            # Recursively process subfolder contents
                            await process_sharepoint_items(subfolder_files, subfolder_path)
                            
                        except Exception as e:
                            logger.error(f"Error processing subfolder {item_name}: {e}")
            
            # Process all items from DDB Group Repository
            await process_sharepoint_items(files_list)
            
            logger.info(f"Found {files_found} files across all folders")
            
            # Enhanced deletion logic with comprehensive logging
            indexed_sp_ids = {}
            orphaned_docs = []

            if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                for doc_id, doc_info in app.state.index.docstore.docs.items():
                    sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                    if sp_id:
                        indexed_sp_ids[sp_id] = doc_id
                    else:
                        # Track orphaned documents (no SharePoint ID)
                        file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                        orphaned_docs.append({
                            "doc_id": doc_id,
                            "file_name": file_name,
                            "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                        })

            logger.info(f"Index analysis: {len(indexed_sp_ids)} documents with SharePoint ID, {len(orphaned_docs)} orphaned documents")

            # Compare and find items to delete
            ids_in_index = set(indexed_sp_ids.keys())
            ids_to_delete = ids_in_index - current_sp_ids
            
            # Enhanced logging for deletion detection
            logger.info(f"DELETION ANALYSIS:")
            logger.info(f"  - SharePoint IDs found in current scan: {len(current_sp_ids)}")
            logger.info(f"  - SharePoint IDs in index: {len(ids_in_index)}")
            logger.info(f"  - IDs to delete: {len(ids_to_delete)}")
            
            if ids_to_delete:
                logger.info(f"Documents marked for deletion:")
                for sp_id in ids_to_delete:
                    doc_id = indexed_sp_ids[sp_id]
                    # Find the document metadata
                    doc_info = None
                    if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                        doc_info = app.state.index.docstore.docs.get(doc_id)
                    
                    file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info and doc_info.metadata else "Unknown"
                    logger.info(f"  - {file_name} (SharePoint ID: {sp_id}, Doc ID: {doc_id})")
            else:
                logger.info("No documents found for deletion")
                
            # Check for Evolution documents specifically
            evolution_docs_in_index = []
            if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
                for doc_id, doc_info in app.state.index.docstore.docs.items():
                    if doc_info.metadata:
                        file_name = doc_info.metadata.get("file_name", "")
                        if "EVOLUTION" in file_name.upper():
                            sp_id = doc_info.metadata.get("sharepoint_id")
                            evolution_docs_in_index.append({
                                "file_name": file_name,
                                "sharepoint_id": sp_id,
                                "doc_id": doc_id,
                                "found_in_sharepoint": sp_id in current_sp_ids if sp_id else False
                            })
            
            if evolution_docs_in_index:
                logger.info(f"EVOLUTION DOCUMENTS ANALYSIS:")
                for doc in evolution_docs_in_index:
                    logger.info(f"  - {doc['file_name']}")
                    logger.info(f"    SharePoint ID: {doc['sharepoint_id']}")
                    logger.info(f"    Doc ID: {doc['doc_id']}")
                    logger.info(f"    Found in current SharePoint scan: {doc['found_in_sharepoint']}")
                    if not doc['found_in_sharepoint']:
                        logger.info(f"    ⚠️  This Evolution document should be deleted!")
                    else:
                        logger.info(f"    ✓ This Evolution document still exists in SharePoint")
            
            deleted_count = 0
            orphaned_deleted_count = 0
            errors = []

            # Delete documents that are no longer in SharePoint
            if ids_to_delete:
                logger.error(f"🚨🚨🚨 SYNC DELETION SECTION: Removing {len(ids_to_delete)} deleted files from index")
                logger.error(f"🎯 IDs to delete: {list(ids_to_delete)}")
                
                for sp_id_to_delete in ids_to_delete:
                    doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                    logger.error(f"🔥 CALLING delete_sharepoint_document_completely({sp_id_to_delete})")
                    logger.error(f"📋 Associated doc_id: {doc_id_to_delete}")
                    
                    # Use comprehensive SharePoint document deletion (fixes all nodes for the document)
                    deletion_success = await delete_sharepoint_document_completely(sp_id_to_delete)
                    
                    logger.error(f"📊 Deletion result for {sp_id_to_delete}: {'SUCCESS' if deletion_success else 'FAILED'}")
                    
                    if deletion_success:
                        logger.error(f"✅ Successfully deleted ALL nodes for SharePoint document {sp_id_to_delete} from index.")
                        deleted_count += 1
                    else:
                        error_msg = f"Failed to completely delete SharePoint document {sp_id_to_delete} (doc_id: {doc_id_to_delete})"
                        logger.error(f"❌ {error_msg}")
                        errors.append(error_msg)
            else:
                logger.error(f"ℹ️ SYNC DELETION SECTION: No documents marked for deletion")

            # Also remove orphaned documents if SharePoint sync is enabled
            if orphaned_docs and settings.USE_SHAREPOINT:
                logger.info(f"Removing {len(orphaned_docs)} orphaned documents (no SharePoint ID)")
                for orphaned_doc in orphaned_docs:
                    doc_id = orphaned_doc["doc_id"]
                    # Use improved deletion with verification
                    deletion_success = await delete_document_with_verification(doc_id)
                    if deletion_success:
                        logger.info(f"Successfully deleted orphaned document: {orphaned_doc['file_name']} (doc_id: {doc_id})")
                        orphaned_deleted_count += 1
                    else:
                        error_msg = f"Failed to delete and verify orphaned document {orphaned_doc['file_name']} (doc_id: {doc_id})"
                        logger.error(error_msg)
                        errors.append(error_msg)
                
            # Persist index changes after deletions
            if deleted_count > 0 or orphaned_deleted_count > 0:
                logger.info("Persisting index changes after deletions...")
                await persist_index()
                logger.info("Index persistence complete.")

            total_deleted = deleted_count + orphaned_deleted_count
            
            # Determine status based on whether there were import errors
            if import_errors and files_imported == 0:
                status = "sync_failed"
                message = f"Manual sync failed: found {files_found} files but failed to import any. Errors: {len(import_errors)}"
            elif import_errors:
                status = "sync_partial"
                message = f"Manual sync partially completed: imported {files_imported} of {files_found} files, {len(import_errors)} failed"
            else:
                status = "sync_completed"
                message = f"Manual sync completed: imported {files_imported} new files, deleted {total_deleted} old files ({deleted_count} SharePoint deletions, {orphaned_deleted_count} orphaned cleanups)"
            
            return {
                "status": status,
                "message": message,
                "sharepoint_files_found": files_found,
                "files_imported": files_imported,
                "deleted_count": deleted_count,
                "orphaned_deleted_count": orphaned_deleted_count,
                "total_deleted": total_deleted,
                "orphaned_documents_found": len(orphaned_docs),
                "indexed_files_checked": len(indexed_sp_ids),
                "target_folder": sync_folder,
                "target_drive_id": known_working_drive_id,
                "import_errors": import_errors,
                "deletion_errors": errors,
                "diagnostic_info": {
                    "success_method": "simplified_sync_using_list_files",
                    "working_drive_id": known_working_drive_id,
                    "total_folders_processed": f"{sync_folder} + subfolders"
                }
            }
            
        except Exception as e:
            logger.error(f"✗ Simplified sync failed: {e}")
            # Fall back to the complex discovery method if simple approach fails
        
        # List ALL available drives across all sites
        logger.info("Step 1: Discovering all available drives...")
        
        # ALSO try the specific IT Storage path directly
        logger.info("=== TESTING SPECIFIC IT STORAGE PATH ===")
        it_storage_drives_info = []
        try:
            it_storage_site_url = "https://ddbgroupcomph-my.sharepoint.com/personal/itstorage_ddbgroup_com_ph"
            it_storage_site_id = await app.state.sharepoint_client.get_site_id(it_storage_site_url, ms_token)
            logger.info(f"✓ IT Storage site ID: {it_storage_site_id}")
            
            # Get drives for this specific site
            it_storage_drives = await app.state.sharepoint_client.list_drives(it_storage_site_id, ms_token)
            logger.info(f"✓ Found {len(it_storage_drives)} drives in IT Storage site")
            
            for i, drive in enumerate(it_storage_drives):
                drive_diagnostic = {
                    "drive_name": drive.get('name', 'Unknown'),
                    "drive_id": drive.get('id', 'Unknown'),
                    "drive_type": drive.get('driveType', 'Unknown'),
                    "site_context": "IT Storage (specific test)",
                    "web_url": drive.get('webUrl', ''),
                    "owner": drive.get('owner', {}),
                    "quota": drive.get('quota', {}),
                    "has_ddb_repository": False,
                    "ddb_repository_check_error": None
                }
                
                logger.info(f"  IT Storage Drive {i+1}: {drive_diagnostic['drive_name']} (ID: {drive_diagnostic['drive_id']})")
                
                # Test if this drive contains DDB Group Repository
                try:
                    ddb_folder = await app.state.sharepoint_client.get_item_by_path(drive.get('id'), "DDB Group Repository", ms_token)
                    drive_diagnostic["has_ddb_repository"] = True
                    drive_diagnostic["ddb_repository_folder_id"] = ddb_folder.get('id')
                    logger.info(f"  ✓ FOUND 'DDB Group Repository' in drive: {drive_diagnostic['drive_name']} (ID: {drive_diagnostic['drive_id']})")
                    logger.info(f"  ✓ Folder ID: {ddb_folder.get('id')}")
                    break
                except Exception as e:
                    drive_diagnostic["ddb_repository_check_error"] = str(e)
                    logger.info(f"  ✗ 'DDB Group Repository' not found in drive {drive_diagnostic['drive_name']}: {e}")
                
                it_storage_drives_info.append(drive_diagnostic)
                    
        except Exception as e:
            error_detail = {
                "error_type": "it_storage_access_failure",
                "error_message": str(e),
                "error_location": "it_storage_site_access",
                "site_url": "https://ddbgroupcomph-my.sharepoint.com/personal/itstorage_ddbgroup_com_ph"
            }
            drive_access_errors.append(error_detail)
            logger.error(f"✗ Failed to access IT Storage site directly: {e}")
        
        logger.info("=== CONTINUING WITH GENERAL DRIVE DISCOVERY ===")
        try:
            all_drives = await app.state.sharepoint_client.list_all_drives(ms_token)
            
            # Log details about all drives found and collect diagnostic info
            logger.info(f"Found {len(all_drives)} total drives across all sites:")
            for i, drive_info in enumerate(all_drives):
                drive_diagnostic = {
                    "drive_name": drive_info.get('drive_name'),
                    "drive_id": drive_info.get('drive_id'),
                    "drive_type": drive_info.get('drive_type'),
                    "site_name": drive_info.get('site_name'),
                    "site_type": drive_info.get('site_type'),
                    "drive_web_url": drive_info.get('drive_web_url'),
                    "site_id": drive_info.get('site_id'),
                    "owner": drive_info.get('owner', 'Unknown'),
                    "quota_used": drive_info.get('quota_used', 'Unknown'),
                    "quota_total": drive_info.get('quota_total', 'Unknown')
                }
                accessible_drives.append(drive_diagnostic)
                
                logger.info(f"  Drive {i+1}:")
                logger.info(f"    Name: {drive_diagnostic['drive_name']}")
                logger.info(f"    ID: {drive_diagnostic['drive_id']}")
                logger.info(f"    Type: {drive_diagnostic['drive_type']}")
                logger.info(f"    Site: {drive_diagnostic['site_name']} ({drive_diagnostic['site_type']})")
                logger.info(f"    URL: {drive_diagnostic['drive_web_url']}")
        
        except Exception as e:
            error_detail = {
                "error_type": "general_drive_discovery_failure",
                "error_message": str(e),
                "error_location": "list_all_drives"
            }
            drive_access_errors.append(error_detail)
            logger.error(f"Failed to discover drives: {e}")
            # Return early with diagnostic info if we can't even discover drives
            return {
                "status": "error",
                "message": f"Cannot discover SharePoint drives: {str(e)}",
                "detail": str(e),
                "diagnostic_info": {
                    "accessible_sites": accessible_sites,
                    "accessible_drives": accessible_drives,
                    "it_storage_drives": it_storage_drives_info,
                    "site_access_errors": site_access_errors,
                    "drive_access_errors": drive_access_errors
                }
            }
        
        # Step 2: Check each drive for "DDB Group Repository" folder
        logger.info("Step 2: Checking each drive for 'DDB Group Repository' folder...")
        target_folder_name = "DDB Group Repository"
        drives_with_target_folder = []
        
        for drive_info in all_drives:
            drive_id = drive_info.get('drive_id')
            drive_name = drive_info.get('drive_name')
            site_name = drive_info.get('site_name')
            
            logger.info(f"Checking drive '{drive_name}' in site '{site_name}' for folder '{target_folder_name}'...")
            
            folder_check = await app.state.sharepoint_client.check_folder_exists_in_drive(
                drive_id, target_folder_name, ms_token
            )
            
            if folder_check.get("found"):
                logger.info(f"✓ FOUND '{target_folder_name}' in drive '{drive_name}' (site: {site_name})")
                logger.info(f"  Folder ID: {folder_check.get('folder_id')}")
                logger.info(f"  Item count: {folder_check.get('item_count')}")
                logger.info(f"  Created: {folder_check.get('created_date')}")
                logger.info(f"  Modified: {folder_check.get('modified_date')}")
                
                drives_with_target_folder.append({
                    "drive_info": drive_info,
                    "folder_details": folder_check
                })
            else:
                logger.info(f"✗ Not found in drive '{drive_name}' (site: {site_name})")
                if folder_check.get("error"):
                    logger.info(f"  Error: {folder_check.get('error')}")
        
        # Summary of findings
        logger.info(f"=== SUMMARY ===")
        logger.info(f"Total drives checked: {len(all_drives)}")
        logger.info(f"Drives containing '{target_folder_name}': {len(drives_with_target_folder)}")
        
        if not drives_with_target_folder:
            # Return detailed information about what was found with diagnostic info
            drive_summary = []
            for drive_info in all_drives:
                drive_summary.append({
                    "drive_name": drive_info.get('drive_name'),
                    "drive_id": drive_info.get('drive_id'),
                    "drive_type": drive_info.get('drive_type'),
                    "site_name": drive_info.get('site_name'),
                    "site_type": drive_info.get('site_type'),
                    "drive_url": drive_info.get('drive_web_url')
                })
            
            return {
                "status": "folder_not_found",
                "message": f"'{target_folder_name}' folder not found in any of the {len(all_drives)} available drives",
                "total_drives_checked": len(all_drives),
                "all_drives": drive_summary,
                "suggestion": "Please check the SharePoint UI to verify the exact location and name of the folder.",
                "diagnostic_info": {
                    "accessible_sites": accessible_sites,
                    "accessible_drives": accessible_drives,
                    "it_storage_drives": it_storage_drives_info,
                    "site_access_errors": site_access_errors,
                    "drive_access_errors": drive_access_errors,
                    "total_sites_found": len(accessible_sites),
                    "total_drives_found": len(accessible_drives),
                    "total_it_storage_drives": len(it_storage_drives_info)
                }
            }
        
        # If multiple drives have the folder, log them all but use the first one
        if len(drives_with_target_folder) > 1:
            logger.warning(f"Multiple drives contain '{target_folder_name}' folder:")
            for match in drives_with_target_folder:
                drive_info = match["drive_info"]
                logger.warning(f"  - {drive_info.get('drive_name')} in {drive_info.get('site_name')}")
            logger.info("Using the first match for sync...")
        
        # Use the first drive that contains the target folder
        selected_match = drives_with_target_folder[0]
        selected_drive_info = selected_match["drive_info"]
        selected_folder_details = selected_match["folder_details"]
        
        drive_id = selected_drive_info.get('drive_id')
        target_item_id = selected_folder_details.get('folder_id')
        
        logger.info(f"=== PROCEEDING WITH SYNC ===")
        logger.info(f"Selected drive: {selected_drive_info.get('drive_name')} (ID: {drive_id})")
        logger.info(f"Selected site: {selected_drive_info.get('site_name')}")
        logger.info(f"Target folder ID: {target_item_id}")
        
        # Continue with the existing sync logic
        current_sp_ids = set()
        deleted_count = 0
        errors = []
        
        # Get all current item IDs from SharePoint AND import new files
        logger.info("Fetching current item IDs from SharePoint target folder...")
        
        # Get all indexed docs first for comparison
        all_indexed_docs = []
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            all_indexed_docs = list(app.state.index.docstore.docs.values())
        
        # Helper function to import a file
        async def import_sharepoint_file(drive_id, file_id, file_name, token):
            """Import a single file from SharePoint into the index."""
            try:
                # Download the file
                file_content = await app.state.sharepoint_client.download_file_content(drive_id, file_id, token)
                
                # Create a temporary file
                import tempfile
                import os
                temp_dir = os.path.join(os.getcwd(), "temp")
                os.makedirs(temp_dir, exist_ok=True)
                
                with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file_name}", dir=temp_dir) as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                # Process the file using existing document processing
                from llama_index.core import SimpleDirectoryReader
                
                # Use SimpleDirectoryReader to load the file
                reader = SimpleDirectoryReader(input_files=[temp_file_path])
                documents = reader.load_data()
                
                # Fix LlamaIndex default exclusions to allow file_name in embeddings
                for doc in documents:
                    doc.excluded_embed_metadata_keys = []
                    doc.excluded_llm_metadata_keys = []
                
                # Add SharePoint metadata to each document
                for doc in documents:
                    doc.metadata.update({
                        "sharepoint_id": file_id,
                        "file_name": file_name,
                        "source": "sharepoint_sync",
                        "drive_id": drive_id,
                        "web_url": f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{file_id}",
                        "created_datetime": datetime.now().isoformat(),
                        "sync_imported": True
                    })
                
                # Add documents to the index
                for doc in documents:
                    app.state.index.insert(doc)
                
                # Clean up temp file
                os.unlink(temp_file_path)
                
                # Persist the index
                app.state.index.storage_context.persist()
                
                return len(documents)
                
            except Exception as e:
                logger.error(f"Error importing file {file_name}: {e}")
                raise e
        
        # Track files found and imported
        files_found = 0
        files_imported = 0
        import_errors = []
        
        # Recursively list files in the target folder AND import new ones
        async for item in app.state.sharepoint_client.list_folder_contents_recursive(
            drive_id, target_item_id, token=ms_token
        ):
            if "file" in item:  # We only care about files for the index
                files_found += 1
                file_id = item.get("id")
                file_name = item.get("name", "Unknown")
                current_sp_ids.add(file_id)
                
                # Check if this file is already indexed
                existing_docs = [
                    doc for doc in all_indexed_docs 
                    if doc.metadata and doc.metadata.get("sharepoint_id") == file_id
                ]
                
                if not existing_docs:
                    # File is new - import it!
                    try:
                        logger.info(f"Importing new file: {file_name} (ID: {file_id})")
                        doc_count = await import_sharepoint_file(drive_id, file_id, file_name, ms_token)
                        logger.info(f"✓ Successfully imported: {file_name} ({doc_count} documents)")
                        files_imported += 1
                    except Exception as e:
                        logger.error(f"Failed to import {file_name}: {e}")
                        import_errors.append(f"{file_name}: {str(e)}")
                else:
                    logger.info(f"File already indexed: {file_name}")
        
        logger.info(f"Found {files_found} files in SharePoint target folder.")
        logger.info(f"Imported {files_imported} new files.")
        
        # 2. Get all sharepoint_ids from the index and identify orphaned documents
        indexed_sp_ids = {}
        orphaned_docs = []

        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                if sp_id:
                    indexed_sp_ids[sp_id] = doc_id
                else:
                    # Track orphaned documents (no SharePoint ID)
                    file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                    orphaned_docs.append({
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                    })

        logger.info(f"Found {len(indexed_sp_ids)} items with sharepoint_id and {len(orphaned_docs)} orphaned documents in the index.")

        # 3. Compare and find items to delete
        ids_in_index = set(indexed_sp_ids.keys())
        ids_to_delete = ids_in_index - current_sp_ids
        logger.info(f"Identified {len(ids_to_delete)} SharePoint items to remove from the index.")

        # 4. Delete items from index
        orphaned_deleted_count = 0

        if ids_to_delete:
            for sp_id_to_delete in ids_to_delete:
                doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                # Use comprehensive SharePoint document deletion (fixes all nodes for the document)
                deletion_success = await delete_sharepoint_document_completely(sp_id_to_delete)
                if deletion_success:
                    logger.info(f"Successfully deleted ALL nodes for SharePoint document {sp_id_to_delete} from index.")
                    deleted_count += 1
                else:
                    error_msg = f"Failed to completely delete SharePoint document {sp_id_to_delete} (doc_id: {doc_id_to_delete})"
                    logger.error(error_msg)
                    errors.append(error_msg)

        # 5. Also remove orphaned documents if SharePoint sync is enabled
        if orphaned_docs and settings.USE_SHAREPOINT:
            logger.info(f"Removing {len(orphaned_docs)} orphaned documents (no SharePoint ID)")
            for orphaned_doc in orphaned_docs:
                doc_id = orphaned_doc["doc_id"]
                # Use improved deletion with verification
                deletion_success = await delete_document_with_verification(doc_id)
                if deletion_success:
                    logger.info(f"Successfully deleted orphaned document: {orphaned_doc['file_name']} (doc_id: {doc_id})")
                    orphaned_deleted_count += 1
                else:
                    error_msg = f"Failed to delete and verify orphaned document {orphaned_doc['file_name']} (doc_id: {doc_id})"
                    logger.error(error_msg)
                    errors.append(error_msg)
        
        # 5. Persist index changes after deletions
        if deleted_count > 0 or orphaned_deleted_count > 0:
            logger.info("Persisting index changes after deletions...")
            await persist_index()
            logger.info("Index persistence complete.")
        else:
            logger.info("No items to delete from the index.")

        logger.info("=== MANUAL SYNC COMPLETE ===")

        total_deleted = deleted_count + orphaned_deleted_count
        
        # Determine status based on whether there were import errors
        if import_errors and files_imported == 0:
            status = "sync_failed"
            message = f"Manual sync failed for folder '{target_folder_name}': found {files_found} files but failed to import any. Errors: {len(import_errors)}"
        elif import_errors:
            status = "sync_partial"
            message = f"Manual sync partially completed for folder '{target_folder_name}': imported {files_imported} of {files_found} files, {len(import_errors)} failed"
        else:
            status = "sync_completed"
            message = f"Manual sync completed successfully for folder '{target_folder_name}' - imported {files_imported} new files, deleted {total_deleted} documents ({deleted_count} SharePoint deletions, {orphaned_deleted_count} orphaned cleanups)"
        
        return JSONResponse(content={
            "status": status,
            "message": message,
            "drive_id": drive_id,
            "drive_name": selected_drive_info.get('drive_name'),
            "site_name": selected_drive_info.get('site_name'),
            "folder_path": target_folder_name,
            "folder_id": target_item_id,
            "files_found": files_found,
            "files_imported": files_imported,
            "import_errors": import_errors,
            "deleted_count": deleted_count,
            "orphaned_deleted_count": orphaned_deleted_count,
            "total_deleted": total_deleted,
            "orphaned_documents_found": len(orphaned_docs),
            "sharepoint_files_found": len(current_sp_ids),
            "indexed_files_before": len(indexed_sp_ids),
            "deleted_count": deleted_count,
            "errors": errors,
            "drives_checked": len(all_drives),
            "drives_with_target_folder": len(drives_with_target_folder),
            "diagnostic_info": {
                "accessible_sites": accessible_sites,
                "accessible_drives": accessible_drives,
                "it_storage_drives": it_storage_drives_info,
                "site_access_errors": site_access_errors,
                "drive_access_errors": drive_access_errors,
                "total_sites_found": len(accessible_sites),
                "total_drives_found": len(accessible_drives),
                "total_it_storage_drives": len(it_storage_drives_info),
                "selected_drive_details": {
                    "drive_info": selected_drive_info,
                    "folder_details": selected_folder_details
                }
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during manual sync: {e}", exc_info=True)
        # Include diagnostic info in exception response
        error_response = {
            "status": "error",
            "message": f"Manual sync failed: {str(e)}",
            "detail": str(e),
            "diagnostic_info": {
                "accessible_sites": accessible_sites,
                "accessible_drives": accessible_drives,
                "it_storage_drives": it_storage_drives_info if 'it_storage_drives_info' in locals() else [],
                "site_access_errors": site_access_errors,
                "drive_access_errors": drive_access_errors,
                "total_sites_found": len(accessible_sites),
                "total_drives_found": len(accessible_drives),
                "error_occurred_during": "sync_operation"
            }
        }
        raise HTTPException(
            status_code=500,
            detail=error_response
        )



@app.get("/query/stream")
async def stream_query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=CurrentUser,
):
    """Stream the RAG system response with real-time chunks."""
    try:
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    def error_stream():
                        yield f"data: {json.dumps({'type': 'error', 'content': 'Failed to load vector index'})}\n\n"
                    return StreamingResponse(
                        error_stream(),
                        media_type="text/plain",
                        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(f"Error creating query engine: {model_error}")
                def error_stream():
                    yield f"data: {json.dumps({'type': 'error', 'content': 'Query engine initialization failed'})}\n\n"
                return StreamingResponse(
                    error_stream(),
                    media_type="text/plain",
                    headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
                )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error("Query engine still not available after initialization attempts")
            def error_stream():
                yield f"data: {json.dumps({'type': 'error', 'content': 'Unable to initialize query engine'})}\n\n"
            return StreamingResponse(
                error_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )

        # Process query and get streaming result
        logger.info(f"Processing streaming query: {query}")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a comprehensive, well-structured answer to the following question. Your response should be thorough, informative, and easy to follow.

CRITICAL INSTRUCTION:
- ONLY answer based on the information provided in the context documents
- If the context doesn't contain relevant information about the question, clearly state: "I don't have information about [topic] in the available documents"
- DO NOT make up or invent information that isn't in the provided context
- DO NOT try to answer questions about topics not covered in the documents

FORMATTING REQUIREMENTS:
- Use markdown formatting with **bold text** for key concepts and important points
- Create bullet points (-) or numbered lists for step-by-step instructions or multiple items
- Use clear paragraph breaks for better readability
- Include relevant subheadings (##) to organize complex topics

CONTENT REQUIREMENTS:
- Provide detailed explanations with context and background information
- Include specific examples, use cases, or scenarios when relevant
- Break down complex concepts into understandable parts
- Explain the reasoning behind recommendations or procedures
- Mention any important considerations, limitations, or exceptions
- Reference relevant company policies, procedures, or guidelines when applicable
- If discussing processes, include the complete workflow from start to finish

DEPTH REQUIREMENTS:
- Go beyond basic answers - provide comprehensive coverage of the topic
- Anticipate follow-up questions and address them proactively
- Include practical implementation details
- Explain both the "what" and the "why" behind information
- Provide actionable insights that users can immediately apply

Question: {query}"""

        def generate_stream():
            try:
                # Use streaming query method - this should be available with streaming=True
                streaming_response = app.state.query_engine.query(enhanced_query)

                # Debug: Log similarity scores for debugging relevance filtering
                if hasattr(streaming_response, 'source_nodes'):
                    logger.info(f"Query: '{query}' - Retrieved {len(streaming_response.source_nodes)} documents after similarity filtering")
                    for i, node in enumerate(streaming_response.source_nodes):
                        # Log similarity score if available
                        score = getattr(node, 'score', 'N/A')
                        filename = node.metadata.get('file_name', 'Unknown')
                        logger.info(f"  Document {i+1}: {filename} - Similarity Score: {score}")

                # Check if no documents were retrieved after similarity filtering
                if hasattr(streaming_response, 'source_nodes') and len(streaming_response.source_nodes) == 0:
                    # No relevant documents found
                    no_docs_message = f"I don't have any relevant documents about \"{query}\" in my knowledge base. Please ensure that relevant documents have been uploaded to the system through the SharePoint sync functionality."
                    
                    chunk_data = {
                        'type': 'chunk',
                        'content': no_docs_message
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    
                    # Send completion signal
                    yield f"data: {json.dumps({'type': 'complete', 'sources': [], 'followUpQuestions': []})}\n\n"
                    return

                # Collect response content for follow-up question analysis
                collected_response_content = []
                
                # Check if response has streaming capability
                if hasattr(streaming_response, 'response_gen'):
                    # Stream the response chunks
                    for chunk in streaming_response.response_gen:
                        chunk_content = str(chunk)
                        collected_response_content.append(chunk_content)
                        chunk_data = {
                            'type': 'chunk',
                            'content': chunk_content
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"
                else:
                    # Fallback to non-streaming if streaming not available
                    response_content = streaming_response.response
                    collected_response_content.append(response_content)
                    # Split response into chunks for pseudo-streaming
                    words = response_content.split(' ')
                    chunk_size = 3  # Send 3 words at a time
                    for i in range(0, len(words), chunk_size):
                        chunk = ' '.join(words[i:i+chunk_size])
                        if i + chunk_size < len(words):
                            chunk += ' '
                        chunk_data = {
                            'type': 'chunk',
                            'content': chunk
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Generate follow-up questions
                follow_up_questions = []
                try:
                    if hasattr(app.state, "llm") and app.state.llm:
                        # Use collected response content from streaming
                        response_content = ''.join(collected_response_content)
                        
                        # Check if response indicates no relevant documents
                        response_lower = response_content.lower()
                        has_no_info_indicators = any(phrase in response_lower for phrase in [
                            "i don't have information",
                            "no information available", 
                            "i don't have any relevant documents",
                            "no relevant documents",
                            "i cannot find",
                            "not available in the documents",
                            "no documents contain"
                        ])
                        
                        # Only generate follow-up questions if we have relevant sources AND the response doesn't indicate lack of information
                        source_nodes = streaming_response.source_nodes if hasattr(streaming_response, 'source_nodes') else []
                        if source_nodes and not has_no_info_indicators:
                            # Get source context information for streaming
                            source_context = ""
                            source_info = []
                            for node in streaming_response.source_nodes[:3]:  # Use top 3 sources
                                metadata = node.node.metadata if hasattr(node, "node") else node.metadata
                                if metadata and 'file_name' in metadata:
                                    source_info.append(metadata['file_name'])
                            
                            if source_info:
                                source_context = f"\n\nThis answer was generated from these company documents: {', '.join(source_info)}. Focus on workplace policies, procedures, and company-related topics."

                            prompt = f"""Based on the comprehensive answer provided about '{query}' and the source documents used, generate exactly 3 strategic follow-up questions that would help the user gain deeper insights. The questions should be thoughtful, actionable, and designed to explore different aspects of the topic.

QUESTION CRITERIA:
- Each question should explore a different dimension (practical application, deeper context, related procedures, compliance aspects, implementation details, etc.)
- Questions should be specific enough to generate comprehensive answers
- Focus on actionable insights that help with real-world workplace scenarios
- Consider both immediate next steps and broader strategic implications
- Questions should encourage deeper exploration of company policies, procedures, and best practices

SOURCE CONTEXT: {source_context}

ORIGINAL QUERY: {query}

COMPREHENSIVE ANSWER PROVIDED:
{response_content}

Generate exactly 3 follow-up questions that would be most valuable for someone seeking to fully understand and implement this information in their work. Output ONLY the questions, each on a new line, without numbering or formatting."""

                            fu_resp = app.state.llm.complete(prompt)  # Use sync version in generator
                            questions_raw = fu_resp.text.strip().split("\n")
                            follow_up_questions = [
                                q.strip(" *-1234567890.) ")
                                for q in questions_raw
                                if q.strip() and len(q.strip()) > 5
                            ][:3]
                        else:
                            logger.info(f"Skipping follow-up question generation for streaming - no relevant sources ({len(source_nodes)}) or response indicates lack of information")
                except Exception as fu_err:
                    logger.warning(f"Follow-up question generation failed: {fu_err}")

                # Apply relevance filtering and source limiting for streaming
                MINIMUM_RELEVANCE_THRESHOLD = 0.15  # 15% relevance threshold
                MAX_SOURCES_DEFAULT = 8  # Limit to top 8 sources by default
                
                # Track source counts for streaming metadata
                total_sources = 0
                relevant_sources_count = 0
                displayed_sources_count = 0
                filtered_source_nodes = []
                
                # Process source nodes with error handling
                try:
                    if hasattr(streaming_response, 'source_nodes') and streaming_response.source_nodes:
                        total_sources = len(streaming_response.source_nodes)
                        
                        # Filter by relevance score
                        relevant_nodes = [
                            node for node in streaming_response.source_nodes 
                            if getattr(node, "score", 0) >= MINIMUM_RELEVANCE_THRESHOLD
                        ]
                        relevant_sources_count = len(relevant_nodes)
                        
                        # Fallback: if no relevant sources, include top 2 sources regardless of threshold
                        if len(relevant_nodes) == 0 and streaming_response.source_nodes:
                            # Sort by score (highest first) and take top 2
                            sorted_nodes = sorted(
                                streaming_response.source_nodes, 
                                key=lambda n: getattr(n, "score", 0), 
                                reverse=True
                            )
                            relevant_nodes = sorted_nodes[:2]
                            logger.info(f"Fallback: No sources above {MINIMUM_RELEVANCE_THRESHOLD:.0%} threshold, including top {len(relevant_nodes)} sources")
                        
                        # Limit to maximum number of sources
                        filtered_source_nodes = relevant_nodes[:MAX_SOURCES_DEFAULT]
                        displayed_sources_count = len(filtered_source_nodes)
                        logger.info(f"Streaming - Sources: {total_sources} total, {relevant_sources_count} relevant, showing {displayed_sources_count}")
                except Exception as source_error:
                    logger.error(f"Error processing sources: {source_error}")
                    # Ensure we still have safe defaults
                    filtered_source_nodes = []
                    total_sources = 0
                    relevant_sources_count = 0
                    displayed_sources_count = 0
                
                # Send completion with metadata (with error handling for source processing)
                processed_source_nodes = []
                for node in filtered_source_nodes:
                    try:
                        node_text = node.node.text if hasattr(node, "node") else node.text
                        processed_source_nodes.append({
                            'text': node_text,
                            'snippet': (
                                node_text[:200] + ("…" if len(node_text) > 200 else "")
                            ) if node_text else "No content available",
                            'score': getattr(node, "score", None),
                            'metadata': node.node.metadata if hasattr(node, "node") else (node.metadata if hasattr(node, "metadata") else {}),
                        })
                    except Exception as node_error:
                        logger.warning(f"Error processing source node: {node_error}")
                        # Include a fallback node so frontend doesn't break
                        processed_source_nodes.append({
                            'text': "Content unavailable",
                            'snippet': "Error processing source content",
                            'score': None,
                            'metadata': {'file_name': 'Unknown Document', 'error': 'processing_failed'},
                        })
                
                completion_data = {
                    'type': 'complete',
                    'source_nodes': processed_source_nodes,
                    'follow_up_questions': follow_up_questions,
                    'source_metadata': {
                        'total_sources': total_sources,
                        'relevant_sources': relevant_sources_count,
                        'displayed_sources': displayed_sources_count,
                        'has_more_sources': relevant_sources_count > displayed_sources_count
                    },
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

            except Exception as e:
                logger.error(f"Error in streaming query: {str(e)}")
                error_data = {
                    'type': 'error',
                    'content': f"An error occurred while processing the query: {str(e)}"
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        logger.error(f"Error in streaming query endpoint: {str(e)}")
        def error_stream():
            error_data = {
                'type': 'error',
                'content': f"An unexpected error occurred: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            error_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )


# Health check endpoint
@app.api_route("/health", methods=["GET", "HEAD"])
async def health_check():
    """Endpoint to check if the service is alive and ready."""
    return JSONResponse(content={"status": "healthy"})


# Root endpoint to render the index page
@app.get("/")
async def root(request: Request):
    """Render the main index template for the application."""
    # Restore original logic
    is_admin = user_is_admin(request.session)
    logger.info(f"[/] Root page loaded. Admin status: {is_admin}")  # Simple log
    return templates.TemplateResponse(
        "index.html", {"request": request, "is_admin": is_admin}
    )


# --- Add missing /documents route ---#
@app.get("/documents", name="documents_page")
async def documents_page_redirect(request: Request, user: dict = CurrentUser):
    """Redirects to the main page. Exists to satisfy url_for calls."""
    logger.info("Redirecting from /documents to root (/)")
    return RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)


# --- End add route ---


# --- Add user_is_admin helper function ---
def user_is_admin(session: dict) -> bool:
    """
    Returns True if the current session belongs to an admin user.
    Admin = basic-auth super-user  OR  Microsoft e-mail in ADMIN_EMAILS.
    """
    logger.info(f"[user_is_admin] Checking admin status for session: {session}")
    admin_emails_env = os.getenv("ADMIN_EMAILS", "")
    admin_emails_set = {
        e.strip().lower() for e in admin_emails_env.split(",") if e.strip()
    }
    logger.info(f"[user_is_admin] ADMIN_EMAILS from env: '{admin_emails_env}'")
    logger.info(f"[user_is_admin] Parsed admin_emails_set: {admin_emails_set}")
    basic_auth_admin_username = settings.USERNAME
    logger.info(
        f"[user_is_admin] Basic auth admin username from settings: '{basic_auth_admin_username}'"
    )

    # Check 1: Basic Auth Admin User
    basic_user_session = session.get("basic_user", {})
    basic_user_authenticated = basic_user_session.get("authenticated")
    basic_user_username = basic_user_session.get("username")
    logger.info(
        f"[user_is_admin] Basic user check: session_username='{basic_user_username}', is_authenticated={basic_user_authenticated}"
    )
    if basic_user_authenticated and basic_user_username == basic_auth_admin_username:
        logger.info("[user_is_admin] Result: True (Basic Auth Match)")
        return True

    # Check 2: Microsoft Logged-in User in Admin Emails List
    ms_user_session = session.get("ms_user", {})
    ms_user_email = ms_user_session.get("email", "").lower()
    logger.info(f"[user_is_admin] Microsoft user check: email='{ms_user_email}'")
    if ms_user_email in admin_emails_set:
        logger.info("[user_is_admin] Result: True (Microsoft Email Match)")
        return True

    logger.info("[user_is_admin] Result: False (No Match)")
    return False


# --- End add helper ---


# New endpoint for manual synchronization
@app.post("/sync/sharepoint/{drive_id}", name="sync_sharepoint_drive")
async def sync_sharepoint_drive(
    request: Request,
    drive_id: str,
    folder_path: str = Query(
        "",
        description="Optional subfolder path within the drive to sync. Default is root.",
    ),
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """Manually syncs the index with the current state of a SharePoint drive/folder."""
    logger.info("=== SYNC_SHAREPOINT_DRIVE ENDPOINT CALLED ===")
    logger.info(f"Request URL: {request.url}")
    logger.info(f"Drive ID received: {drive_id}")
    logger.info(f"Folder path received: '{folder_path or 'root'}'")
    
    # OVERRIDE: Always use the correct drive ID from SharePoint UI
    original_drive_id = drive_id
    correct_drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
    
    # Always override to use the working drive ID
    drive_id = correct_drive_id
    logger.info(f"FORCE OVERRIDING drive ID from {original_drive_id} to {correct_drive_id}")
    
    # Also ensure folder_path is set correctly
    if not folder_path or folder_path == "":
        folder_path = "DDB Group Repository"
        logger.info(f"Setting folder_path to: {folder_path}")
    
    logger.info(
        f"Starting manual sync for Drive ID: {drive_id}, Folder Path: '{folder_path or 'root'}'"
    )
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for sync operation")
        raise HTTPException(status_code=503, detail="Index is not ready.")
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client not available for sync operation")
        raise HTTPException(
            status_code=503, detail="SharePoint client is not configured or ready."
        )

    current_sp_ids = set()
    deleted_count = 0
    errors = []

    try:
        # 1. Get all current item IDs from SharePoint
        logger.info("Fetching current item IDs from SharePoint...")
        # Determine target folder ID (root or specific subfolder)
        target_item_id = "root"  # Default to root
        if folder_path:
            try:
                folder_item = await app.state.sharepoint_client.get_item_by_path(
                    drive_id, folder_path, token=ms_token
                )
                if folder_item and "id" in folder_item:
                    target_item_id = folder_item["id"]
                    logger.info(
                        f"Targeting folder '{folder_path}' with ID: {target_item_id}"
                    )
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Folder path '{folder_path}' not found in drive {drive_id}.",
                    )
            except Exception as e:
                logger.error(
                    f"Error getting folder ID for path '{folder_path}': {e}",
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to resolve folder path '{folder_path}'.",
                )

        # Helper function to import a file
        async def import_sharepoint_file(drive_id, file_id, file_name, token):
            """Import a single file from SharePoint into the index."""
            try:
                # Download the file
                file_content = await app.state.sharepoint_client.download_file_content(drive_id, file_id, token)
                
                # Create a temporary file
                import tempfile
                import os
                temp_dir = os.path.join(os.getcwd(), "temp")
                os.makedirs(temp_dir, exist_ok=True)
                
                with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file_name}", dir=temp_dir) as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                # Process the file using existing document processing
                from llama_index.core import SimpleDirectoryReader
                from pathlib import Path
                
                # Use SimpleDirectoryReader to load the file
                reader = SimpleDirectoryReader(input_files=[temp_file_path])
                documents = reader.load_data()
                
                # Fix LlamaIndex default exclusions to allow file_name in embeddings
                for doc in documents:
                    doc.excluded_embed_metadata_keys = []
                    doc.excluded_llm_metadata_keys = []
                
                # Add SharePoint metadata to each document
                for doc in documents:
                    doc.metadata.update({
                        "sharepoint_id": file_id,
                        "file_name": file_name,
                        "source": "sharepoint_sync",
                        "drive_id": drive_id,
                        "web_url": f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{file_id}",
                        "created_datetime": datetime.now().isoformat(),
                        "sync_imported": True
                    })
                
                # Add documents to the index
                for doc in documents:
                    app.state.index.insert(doc)
                
                # Clean up temp file
                os.unlink(temp_file_path)
                
                # Persist the index
                app.state.index.storage_context.persist()
                
                return len(documents)
                
            except Exception as e:
                logger.error(f"Error importing file {file_name}: {e}")
                raise e
        
        # Get all indexed docs for deletion detection
        all_indexed_docs = []
        if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
            all_indexed_docs = list(app.state.index.docstore.docs.values())
        
        # Recursively list files AND import them
        files_found = 0
        files_imported = 0
        import_errors = []
        
        async for item in app.state.sharepoint_client.list_folder_contents_recursive(
            drive_id, target_item_id, token=ms_token
        ):
            if "file" in item:  # We only care about files for the index
                files_found += 1
                file_id = item.get("id")
                file_name = item.get("name", "Unknown")
                
                # Check if this file is already indexed
                existing_docs = [
                    doc for doc in all_indexed_docs 
                    if doc.metadata.get("sharepoint_id") == file_id
                ]
                
                if not existing_docs:
                    # File is new - import it!
                    try:
                        logger.info(f"Importing new file: {file_name} (ID: {file_id})")
                        doc_count = await import_sharepoint_file(drive_id, file_id, file_name, ms_token)
                        logger.info(f"✓ Successfully imported: {file_name} ({doc_count} documents)")
                        files_imported += 1
                    except Exception as e:
                        logger.error(f"Failed to import {file_name}: {e}")
                        import_errors.append(f"{file_name}: {str(e)}")
                else:
                    logger.info(f"File already indexed: {file_name}")
                    
                # Add to current files list (for deletion detection)
                current_sp_ids.add(item.get("id"))

        logger.info(
            f"Found {len(current_sp_ids)} current file items in SharePoint target."
        )

        # 2. Get all sharepoint_ids from the index and identify orphaned documents
        indexed_sp_ids = {}
        orphaned_docs = []

        if hasattr(app.state.index, "docstore") and hasattr(
            app.state.index.docstore, "docs"
        ):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                if sp_id:
                    indexed_sp_ids[sp_id] = doc_id
                else:
                    # Track orphaned documents (no SharePoint ID)
                    file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "No metadata"
                    orphaned_docs.append({
                        "doc_id": doc_id,
                        "file_name": file_name,
                        "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
                    })

        logger.info(
            f"Found {len(indexed_sp_ids)} items with sharepoint_id and {len(orphaned_docs)} orphaned documents in the index."
        )

        # 3. Compare and find items to delete
        ids_in_index = set(indexed_sp_ids.keys())
        ids_to_delete = ids_in_index - current_sp_ids
        logger.info(f"Identified {len(ids_to_delete)} SharePoint items to remove from the index.")

        # 4. Delete items from index
        orphaned_deleted_count = 0

        if ids_to_delete:
            for sp_id_to_delete in ids_to_delete:
                doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                # Use comprehensive SharePoint document deletion (fixes all nodes for the document)
                deletion_success = await delete_sharepoint_document_completely(sp_id_to_delete)
                if deletion_success:
                    logger.info(f"Successfully deleted ALL nodes for SharePoint document {sp_id_to_delete} from index.")
                    deleted_count += 1
                else:
                    error_msg = f"Failed to completely delete SharePoint document {sp_id_to_delete} (doc_id: {doc_id_to_delete})"
                    logger.error(error_msg)
                    errors.append(error_msg)

        # 5. Also remove orphaned documents if SharePoint sync is enabled
        if orphaned_docs and settings.USE_SHAREPOINT:
            logger.info(f"Removing {len(orphaned_docs)} orphaned documents (no SharePoint ID)")
            for orphaned_doc in orphaned_docs:
                doc_id = orphaned_doc["doc_id"]
                # Use improved deletion with verification
                deletion_success = await delete_document_with_verification(doc_id)
                if deletion_success:
                    logger.info(f"Successfully deleted orphaned document: {orphaned_doc['file_name']} (doc_id: {doc_id})")
                    orphaned_deleted_count += 1
                else:
                    error_msg = f"Failed to delete and verify orphaned document {orphaned_doc['file_name']} (doc_id: {doc_id})"
                    logger.error(error_msg)
                    errors.append(error_msg)

        # 5. Persist index changes after deletions
        if deleted_count > 0 or orphaned_deleted_count > 0:
            logger.info("Persisting index changes after deletions...")
            await persist_index()
            logger.info("Index persistence complete.")
        else:
            logger.info("No actual deletions performed, skipping persistence.")
            
        if not orphaned_docs or not settings.USE_SHAREPOINT:
            logger.info("No items to delete from the index.")

        total_deleted = deleted_count + orphaned_deleted_count
        
        # Determine status based on whether there were import errors
        if import_errors and files_imported == 0:
            status = "sync_failed"
            message = f"Sync failed: found {files_found} files but failed to import any. Errors: {len(import_errors)}"
        elif import_errors:
            status = "sync_partial"
            message = f"Sync partially completed: imported {files_imported} of {files_found} files, {len(import_errors)} failed"
        else:
            status = "sync_completed"
            message = f"Sync completed: imported {files_imported} new files, deleted {total_deleted} old files ({deleted_count} SharePoint deletions, {orphaned_deleted_count} orphaned cleanups)"
        
        return JSONResponse(
            content={
                "status": status,
                "message": message,
                "drive_id": drive_id,
                "folder_path": folder_path or "root",
                "sharepoint_files_found": files_found,
                "files_imported": files_imported,
                "indexed_items_checked": len(indexed_sp_ids),
                "items_deleted_from_index": deleted_count,
                "orphaned_deleted_count": orphaned_deleted_count,
                "total_deleted": total_deleted,
                "orphaned_documents_found": len(orphaned_docs),
                "import_errors": import_errors,
                "deletion_errors": errors,
            }
        )

    except HTTPException as he:
        raise he  # Re-raise HTTP exceptions
    except asyncio.TimeoutError:
        logger.error("SharePoint sync timed out")
        raise HTTPException(
            status_code=504, 
            detail="Sync operation timed out. Please try again with a smaller folder or check network connectivity."
        )
    except ConnectionError as ce:
        logger.error(f"Connection error during SharePoint sync: {ce}")
        raise HTTPException(
            status_code=503,
            detail="Unable to connect to SharePoint. Please check network connectivity and try again."
        )
    except PermissionError as pe:
        logger.error(f"Permission error during SharePoint sync: {pe}")
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to access SharePoint drive or folder. Please contact your administrator."
        )
    except Exception as e:
        error_msg = str(e).lower()
        
        # Handle specific SharePoint API errors
        if "401" in error_msg or "unauthorized" in error_msg:
            logger.error("Authentication failed during sync")
            raise HTTPException(
                status_code=401,
                detail="Authentication expired. Please log out and log back in to refresh your session."
            )
        elif "403" in error_msg or "forbidden" in error_msg:
            logger.error("Permission denied during sync")
            raise HTTPException(
                status_code=403,
                detail="Access denied to SharePoint resources. Please check your permissions."
            )
        elif "404" in error_msg or "not found" in error_msg:
            logger.error("SharePoint resource not found during sync")
            raise HTTPException(
                status_code=404,
                detail=f"SharePoint drive or folder not found. Please verify the drive ID and folder path."
            )
        elif "429" in error_msg or "throttle" in error_msg:
            logger.error("Rate limit exceeded during sync")
            raise HTTPException(
                status_code=429,
                detail="SharePoint API rate limit exceeded. Please wait a few minutes and try again."
            )
        else:
            logger.error(f"Unexpected error during SharePoint sync: {e}", exc_info=True)
            raise HTTPException(
                status_code=500, 
                detail=f"Sync failed due to an unexpected error. If this persists, please contact support. Error: {str(e)[:100]}"
            )


@app.get("/api/documents/{document_id}/view")
async def view_document(
    document_id: str,
    user=CurrentUser,
    ms_token=MsTokenDep,
):
    """
    Serve a document for viewing in the browser.
    Supports both doc_id and sharepoint_id as identifiers.
    """
    try:
        # Check if we have the required dependencies
        if not hasattr(app.state, 'index') or not app.state.index:
            raise HTTPException(status_code=503, detail="Document index not available")
        
        if not hasattr(app.state, 'sharepoint_client') or not app.state.sharepoint_client:
            raise HTTPException(status_code=503, detail="SharePoint client not available")
        
        if not ms_token:
            raise HTTPException(status_code=401, detail="Microsoft authentication required")
        
        # Get all indexed documents to find the requested one
        indexed_docs = []
        docstore = app.state.index.docstore
        
        for doc_id, doc_data in docstore.docs.items():
            if hasattr(doc_data, 'metadata') and doc_data.metadata:
                doc_info = {
                    "doc_id": doc_id,
                    "metadata": doc_data.metadata
                }
                indexed_docs.append(doc_info)
        
        # Find the document by either doc_id or sharepoint_id
        target_doc = None
        for doc in indexed_docs:
            if (doc["doc_id"] == document_id or 
                doc["metadata"].get("sharepoint_id") == document_id):
                target_doc = doc
                break
        
        if not target_doc:
            raise HTTPException(
                status_code=404, 
                detail=f"Document not found: {document_id}"
            )
        
        # Extract SharePoint information
        sharepoint_id = target_doc["metadata"].get("sharepoint_id")
        drive_id = target_doc["metadata"].get("drive_id") 
        file_name = target_doc["metadata"].get("file_name", "document")
        
        if not sharepoint_id or not drive_id:
            raise HTTPException(
                status_code=400,
                detail="Document missing required SharePoint metadata"
            )
        
        # Download the file from SharePoint
        try:
            file_content = await app.state.sharepoint_client.download_file_content(
                drive_id, sharepoint_id, ms_token
            )
        except Exception as e:
            logger.error(f"Failed to download file from SharePoint: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve document from SharePoint"
            )
        
        # Determine MIME type based on file extension
        import mimetypes
        from pathlib import Path
        
        file_extension = Path(file_name).suffix.lower()
        
        # Comprehensive MIME type mapping
        mime_mapping = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.csv': 'text/csv',
            '.json': 'application/json',
            '.xml': 'text/xml',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.svg': 'image/svg+xml',
        }
        
        content_type = mime_mapping.get(file_extension)
        if not content_type:
            # Fallback to mimetypes library
            content_type, _ = mimetypes.guess_type(file_name)
            if not content_type:
                content_type = 'application/octet-stream'
        
        # Set up response headers
        headers = {
            "Content-Type": content_type,
            "Cache-Control": "private, max-age=3600",  # Cache for 1 hour
        }
        
        # For PDFs and images, use inline display; for others, suggest download
        if file_extension in ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.svg']:
            headers["Content-Disposition"] = f'inline; filename="{file_name}"'
        else:
            headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        
        # Return the file content as streaming response
        from fastapi.responses import Response
        return Response(
            content=file_content,
            media_type=content_type,
            headers=headers
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error in view_document: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Internal server error while retrieving document"
        )
